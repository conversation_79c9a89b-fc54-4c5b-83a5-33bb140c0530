from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from decimal import Decimal

from products.models import Category, Brand, Product, Banner
from users.models import UserProfile, VIPLevel
from orders.models import DiscountRule


class Command(BaseCommand):
    help = '初始化示例数据'

    def handle(self, *args, **options):
        self.stdout.write('开始初始化示例数据...')
        
        # 创建VIP等级
        self.create_vip_levels()
        
        # 创建优惠规则
        self.create_discount_rules()
        
        # 创建品牌
        self.create_brands()
        
        # 创建商品分类
        self.create_categories()
        
        # 创建示例商品
        self.create_products()
        
        # 创建轮播图
        self.create_banners()
        
        # 创建测试用户
        self.create_test_users()
        
        self.stdout.write(self.style.SUCCESS('示例数据初始化完成！'))

    def create_vip_levels(self):
        """创建VIP等级"""
        vip_levels_data = [
            {
                'name': '青铜会员',
                'level': 1,
                'min_consumption': Decimal('0'),
                'discount_rate': Decimal('1.0000'),
                'benefits': '基础会员权益'
            },
            {
                'name': '白银会员',
                'level': 2,
                'min_consumption': Decimal('1000'),
                'discount_rate': Decimal('0.9800'),
                'benefits': '98折优惠，专属客服'
            },
            {
                'name': '黄金会员',
                'level': 3,
                'min_consumption': Decimal('5000'),
                'discount_rate': Decimal('0.9500'),
                'benefits': '95折优惠，免费配送，生日礼品'
            },
            {
                'name': '钻石会员',
                'level': 4,
                'min_consumption': Decimal('20000'),
                'discount_rate': Decimal('0.9000'),
                'benefits': '9折优惠，优先发货，专属活动'
            },
        ]
        
        for data in vip_levels_data:
            level, created = VIPLevel.objects.get_or_create(
                level=data['level'],
                defaults=data
            )
            if created:
                self.stdout.write(f'创建VIP等级: {level.name}')

    def create_discount_rules(self):
        """创建优惠规则"""
        rules_data = [
            {
                'name': '满100减10',
                'min_amount': Decimal('100'),
                'discount_amount': Decimal('10'),
            },
            {
                'name': '满300减50',
                'min_amount': Decimal('300'),
                'discount_amount': Decimal('50'),
            },
            {
                'name': '满1000减200',
                'min_amount': Decimal('1000'),
                'discount_amount': Decimal('200'),
            },
        ]
        
        for data in rules_data:
            rule, created = DiscountRule.objects.get_or_create(
                name=data['name'],
                defaults=data
            )
            if created:
                self.stdout.write(f'创建优惠规则: {rule.name}')

    def create_brands(self):
        """创建品牌"""
        brands_data = [
            {'name': '苹果', 'description': 'Apple Inc.'},
            {'name': '小米', 'description': '小米科技'},
            {'name': '华为', 'description': '华为技术有限公司'},
            {'name': '三星', 'description': 'Samsung'},
            {'name': '戴森', 'description': 'Dyson'},
            {'name': '优衣库', 'description': 'UNIQLO'},
            {'name': 'Nike', 'description': 'Nike Inc.'},
        ]
        
        for data in brands_data:
            brand, created = Brand.objects.get_or_create(
                name=data['name'],
                defaults=data
            )
            if created:
                self.stdout.write(f'创建品牌: {brand.name}')

    def create_categories(self):
        """创建商品分类"""
        # 主分类
        main_categories_data = [
            {'name': '电子产品', 'sort_order': 1},
            {'name': '服装鞋帽', 'sort_order': 2},
            {'name': '家居用品', 'sort_order': 3},
            {'name': '图书音像', 'sort_order': 4},
            {'name': '运动户外', 'sort_order': 5},
            {'name': '美妆护肤', 'sort_order': 6},
            {'name': '食品饮料', 'sort_order': 7},
            {'name': '母婴用品', 'sort_order': 8},
            {'name': '汽车用品', 'sort_order': 9},
            {'name': '办公用品', 'sort_order': 10},
        ]
        
        main_categories = {}
        for data in main_categories_data:
            category, created = Category.objects.get_or_create(
                name=data['name'],
                parent=None,
                defaults=data
            )
            main_categories[data['name']] = category
            if created:
                self.stdout.write(f'创建主分类: {category.name}')
        
        # 子分类
        sub_categories_data = [
            # 电子产品子分类
            {'name': '手机通讯', 'parent': '电子产品'},
            {'name': '电脑办公', 'parent': '电子产品'},
            {'name': '数码配件', 'parent': '电子产品'},
            
            # 服装鞋帽子分类
            {'name': '男装', 'parent': '服装鞋帽'},
            {'name': '女装', 'parent': '服装鞋帽'},
            {'name': '运动鞋', 'parent': '服装鞋帽'},
            
            # 家居用品子分类
            {'name': '家用电器', 'parent': '家居用品'},
            {'name': '家具', 'parent': '家居用品'},
            {'name': '厨具', 'parent': '家居用品'},
        ]
        
        for data in sub_categories_data:
            parent_category = main_categories.get(data['parent'])
            if parent_category:
                category, created = Category.objects.get_or_create(
                    name=data['name'],
                    parent=parent_category,
                    defaults={'sort_order': 0, 'is_active': True}
                )
                if created:
                    self.stdout.write(f'创建子分类: {category.name}')

    def create_products(self):
        """创建示例商品"""
        # 获取分类和品牌
        phone_category = Category.objects.get(name='手机通讯')
        computer_category = Category.objects.get(name='电脑办公')
        clothing_category = Category.objects.get(name='男装')
        appliance_category = Category.objects.get(name='家用电器')
        
        apple_brand = Brand.objects.get(name='苹果')
        xiaomi_brand = Brand.objects.get(name='小米')
        dyson_brand = Brand.objects.get(name='戴森')
        
        products_data = [
            {
                'name': 'iPhone 15 Pro Max',
                'category': phone_category,
                'brand': apple_brand,
                'sku': 'IP15PM001',
                'description': '苹果最新旗舰手机，搭载A17 Pro芯片，钛金属机身，专业级摄影系统。',
                'price': Decimal('9999.00'),
                'original_price': Decimal('10999.00'),
                'stock': 50,
                'is_hot': True,
                'is_new': True,
            },
            {
                'name': '小米14 Ultra',
                'category': phone_category,
                'brand': xiaomi_brand,
                'sku': 'MI14U001',
                'description': '小米年度旗舰，徕卡影像，骁龙8 Gen3处理器，120W快充。',
                'price': Decimal('5999.00'),
                'original_price': Decimal('6499.00'),
                'stock': 30,
                'is_hot': True,
            },
            {
                'name': 'MacBook Pro 16英寸',
                'category': computer_category,
                'brand': apple_brand,
                'sku': 'MBP16001',
                'description': 'M3 Max芯片，36GB内存，1TB存储，专业创作利器。',
                'price': Decimal('25999.00'),
                'stock': 20,
                'is_new': True,
            },
            {
                'name': '戴森V15无线吸尘器',
                'category': appliance_category,
                'brand': dyson_brand,
                'sku': 'DYV15001',
                'description': '强劲吸力，激光显尘，智能感应，家庭清洁好帮手。',
                'price': Decimal('3990.00'),
                'original_price': Decimal('4490.00'),
                'stock': 25,
                'is_hot': True,
            },
        ]
        
        for data in products_data:
            product, created = Product.objects.get_or_create(
                sku=data['sku'],
                defaults=data
            )
            if created:
                self.stdout.write(f'创建商品: {product.name}')

    def create_banners(self):
        """创建轮播图"""
        banners_data = [
            {'title': '新年大促销', 'sort_order': 1},
            {'title': '春季新品上市', 'sort_order': 2},
            {'title': '电子产品专场', 'sort_order': 3},
        ]
        
        for data in banners_data:
            banner, created = Banner.objects.get_or_create(
                title=data['title'],
                defaults=data
            )
            if created:
                self.stdout.write(f'创建轮播图: {banner.title}')

    def create_test_users(self):
        """创建测试用户"""
        # 创建普通测试用户
        test_user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'first_name': '测试',
                'last_name': '用户',
            }
        )
        if created:
            test_user.set_password('test123456')
            test_user.save()
            
            # 创建用户资料
            UserProfile.objects.create(
                user=test_user,
                phone='13800138000',
                gender='male',
                balance=Decimal('1000.00')
            )
            self.stdout.write(f'创建测试用户: {test_user.username}')
        
        # 创建VIP测试用户
        vip_user, created = User.objects.get_or_create(
            username='vipuser',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'VIP',
                'last_name': '用户',
            }
        )
        if created:
            vip_user.set_password('vip123456')
            vip_user.save()
            
            # 创建VIP用户资料
            gold_level = VIPLevel.objects.get(level=3)
            UserProfile.objects.create(
                user=vip_user,
                phone='13900139000',
                gender='female',
                vip_level=gold_level,
                total_consumption=Decimal('8000.00'),
                balance=Decimal('5000.00')
            )
            self.stdout.write(f'创建VIP用户: {vip_user.username}')

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表盘 - 智能电商管理后台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FF8C42;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
        }
        
        .stats-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-card .card-body {
            padding: 1.5rem;
        }
        
        .stats-card i {
            color: var(--primary-color);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .text-primary {
            color: var(--primary-color) !important;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-cogs me-2"></i>智能电商管理后台
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>{{ user.username }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{% url 'admin_panel:logout' %}">
                            <i class="fas fa-sign-out-alt me-2"></i>退出登录
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">管理菜单</h6>
                        <div class="list-group list-group-flush">
                            <a href="{% url 'admin_panel:dashboard' %}" class="list-group-item list-group-item-action active">
                                <i class="fas fa-tachometer-alt me-2"></i>仪表盘
                            </a>
                            <a href="/admin/" class="list-group-item list-group-item-action">
                                <i class="fas fa-cog me-2"></i>Django管理
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要内容 -->
            <div class="col-md-10">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-tachometer-alt me-2"></i>仪表盘</h1>
                    <div class="text-muted">
                        <i class="fas fa-clock me-1"></i>{{ "now"|date:"Y-m-d H:i" }}
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h3>{{ total_users }}</h3>
                                <p class="mb-0">普通用户</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-user-shield fa-2x mb-2"></i>
                                <h3>{{ total_admins }}</h3>
                                <p class="mb-0">管理员</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-box fa-2x mb-2"></i>
                                <h3>0</h3>
                                <p class="mb-0">商品总数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                <h3>0</h3>
                                <p class="mb-0">订单总数</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近操作日志 -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list me-2"></i>最近操作日志</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>操作人</th>
                                        <th>操作类型</th>
                                        <th>描述</th>
                                        <th>IP地址</th>
                                        <th>操作时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for log in recent_logs %}
                                    <tr>
                                        <td>{{ log.admin_user.username }}</td>
                                        <td>
                                            {% if log.action == 'login' %}
                                                <span class="badge bg-success">{{ log.get_action_display }}</span>
                                            {% elif log.action == 'logout' %}
                                                <span class="badge bg-secondary">{{ log.get_action_display }}</span>
                                            {% elif log.action == 'create' %}
                                                <span class="badge bg-primary">{{ log.get_action_display }}</span>
                                            {% elif log.action == 'update' %}
                                                <span class="badge bg-warning">{{ log.get_action_display }}</span>
                                            {% elif log.action == 'delete' %}
                                                <span class="badge bg-danger">{{ log.get_action_display }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ log.description }}</td>
                                        <td>{{ log.ip_address }}</td>
                                        <td>{{ log.created_at|date:"m-d H:i" }}</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">暂无操作日志</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-bolt me-2"></i>快速操作</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <a href="/admin/" class="btn btn-outline-primary w-100">
                                            <i class="fas fa-cog me-2"></i>Django管理
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="http://127.0.0.1:8001/" target="_blank" class="btn btn-outline-success w-100">
                                            <i class="fas fa-external-link-alt me-2"></i>查看前台
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-outline-info w-100" disabled>
                                            <i class="fas fa-chart-bar me-2"></i>数据统计
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-outline-warning w-100" disabled>
                                            <i class="fas fa-cogs me-2"></i>系统设置
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
    $(document).ready(function() {
        // 实时更新时间
        function updateTime() {
            var now = new Date();
            var timeString = now.getFullYear() + '-' + 
                            String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                            String(now.getDate()).padStart(2, '0') + ' ' +
                            String(now.getHours()).padStart(2, '0') + ':' + 
                            String(now.getMinutes()).padStart(2, '0');
            $('.text-muted .fas.fa-clock').parent().html('<i class="fas fa-clock me-1"></i>' + timeString);
        }
        
        // 每分钟更新一次时间
        setInterval(updateTime, 60000);
    });
    </script>
</body>
</html>

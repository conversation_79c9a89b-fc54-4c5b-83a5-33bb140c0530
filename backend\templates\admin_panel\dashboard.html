{% extends 'admin_panel/base.html' %}

{% block title %}首页概览 - 智能电商管理后台{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }

    .stats-card:hover {
        transform: translateY(-5px);
    }

    .stats-card .card-body {
        padding: 1.5rem;
    }

    .stats-card i {
        color: var(--primary-color);
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-home me-2"></i>首页概览</h1>
        <div class="text-muted">
            <i class="fas fa-clock me-1"></i>{{ "now"|date:"Y-m-d H:i" }}
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h3>{{ total_users }}</h3>
                <p class="mb-0">普通用户</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-user-shield fa-2x mb-2"></i>
                <h3>{{ total_admins }}</h3>
                <p class="mb-0">管理员</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-box fa-2x mb-2"></i>
                <h3>0</h3>
                <p class="mb-0">商品总数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                <h3>0</h3>
                <p class="mb-0">订单总数</p>
            </div>
        </div>
    </div>
</div>

<!-- 最近操作日志 -->
<div class="card content-card">
    <div class="card-header">
        <h5><i class="fas fa-list me-2"></i>最近操作日志</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>操作人</th>
                        <th>操作类型</th>
                        <th>描述</th>
                        <th>IP地址</th>
                        <th>操作时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in recent_logs %}
                    <tr>
                        <td>{{ log.admin_user.username }}</td>
                        <td>
                            {% if log.action == 'login' %}
                                <span class="badge bg-success">{{ log.get_action_display }}</span>
                            {% elif log.action == 'logout' %}
                                <span class="badge bg-secondary">{{ log.get_action_display }}</span>
                            {% elif log.action == 'create' %}
                                <span class="badge bg-primary">{{ log.get_action_display }}</span>
                            {% elif log.action == 'update' %}
                                <span class="badge bg-warning">{{ log.get_action_display }}</span>
                            {% elif log.action == 'delete' %}
                                <span class="badge bg-danger">{{ log.get_action_display }}</span>
                            {% endif %}
                        </td>
                        <td>{{ log.description }}</td>
                        <td>{{ log.ip_address }}</td>
                        <td>{{ log.created_at|date:"m-d H:i" }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center text-muted">暂无操作日志</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="card content-card mt-4">
    <div class="card-header">
        <h5><i class="fas fa-bolt me-2"></i>快速操作</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3 mb-2">
                <a href="{% url 'admin_panel:category_list' %}" class="btn btn-outline-primary w-100">
                    <i class="fas fa-tags me-2"></i>商品分类
                </a>
            </div>
            <div class="col-md-3 mb-2">
                <a href="{% url 'admin_panel:product_list' %}" class="btn btn-outline-success w-100">
                    <i class="fas fa-box me-2"></i>商品管理
                </a>
            </div>
            <div class="col-md-3 mb-2">
                <a href="http://127.0.0.1:8001/" target="_blank" class="btn btn-outline-info w-100">
                    <i class="fas fa-external-link-alt me-2"></i>查看前台
                </a>
            </div>
            <div class="col-md-3 mb-2">
                <a href="/admin/" target="_blank" class="btn btn-outline-warning w-100">
                    <i class="fas fa-cog me-2"></i>Django管理
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 实时更新时间
    function updateTime() {
        var now = new Date();
        var timeString = now.getFullYear() + '-' +
                        String(now.getMonth() + 1).padStart(2, '0') + '-' +
                        String(now.getDate()).padStart(2, '0') + ' ' +
                        String(now.getHours()).padStart(2, '0') + ':' +
                        String(now.getMinutes()).padStart(2, '0');
        $('.text-muted .fas.fa-clock').parent().html('<i class="fas fa-clock me-1"></i>' + timeString);
    }

    // 每分钟更新一次时间
    setInterval(updateTime, 60000);
});
</script>
{% endblock %}

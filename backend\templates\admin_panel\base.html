<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}智能电商管理后台{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FF8C42;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
        }
        
        .sidebar {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 20px;
        }
        
        .sidebar .list-group-item {
            border: none;
            border-radius: 0;
            padding: 0.75rem 1rem;
            transition: all 0.2s;
        }
        
        .sidebar .list-group-item:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }
        
        .sidebar .list-group-item.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .content-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .text-primary {
            color: var(--primary-color) !important;
        }
        
        .table th {
            border-top: none;
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .page-header {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'admin_panel:dashboard' %}">
                <i class="fas fa-cogs me-2"></i>智能电商管理后台
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>{{ user.username }}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{% url 'admin_panel:profile' %}">
                            <i class="fas fa-user-cog me-2"></i>个人中心
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{% url 'admin_panel:logout' %}">
                            <i class="fas fa-sign-out-alt me-2"></i>退出登录
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2">
                <div class="sidebar">
                    <div class="card-body">
                        <h6 class="card-title mb-3">管理菜单</h6>
                        <div class="list-group list-group-flush">
                            <a href="{% url 'admin_panel:dashboard' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                                <i class="fas fa-home me-2"></i>首页概览
                            </a>
                            <a href="{% url 'admin_panel:category_list' %}" class="list-group-item list-group-item-action {% if 'category' in request.resolver_match.url_name %}active{% endif %}">
                                <i class="fas fa-tags me-2"></i>商品分类
                            </a>
                            <a href="{% url 'admin_panel:product_list' %}" class="list-group-item list-group-item-action {% if 'product' in request.resolver_match.url_name %}active{% endif %}">
                                <i class="fas fa-box me-2"></i>商品管理
                            </a>
                            <a href="{% url 'admin_panel:order_list' %}" class="list-group-item list-group-item-action {% if 'order' in request.resolver_match.url_name %}active{% endif %}">
                                <i class="fas fa-shopping-cart me-2"></i>订单管理
                            </a>
                            <a href="{% url 'admin_panel:user_list' %}" class="list-group-item list-group-item-action {% if 'user' in request.resolver_match.url_name %}active{% endif %}">
                                <i class="fas fa-users me-2"></i>用户管理
                            </a>
                            <a href="{% url 'admin_panel:profile' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'profile' %}active{% endif %}">
                                <i class="fas fa-user-cog me-2"></i>个人中心
                            </a>
                            <a href="/admin/" class="list-group-item list-group-item-action" target="_blank">
                                <i class="fas fa-cog me-2"></i>Django管理
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要内容 -->
            <div class="col-md-10">
                <!-- 消息提示 -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>

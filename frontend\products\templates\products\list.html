{% extends 'base.html' %}

{% block title %}商品列表 - 智能电商{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- 页面标题 -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">商品列表</h1>
        <p class="text-gray-600">共找到 {{ total_count }} 件商品</p>
    </div>

    <!-- 排序和视图切换 -->
    <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-4">
            <span class="text-gray-600">排序方式：</span>
            <select class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-primary/30">
                <option>默认排序</option>
                <option>价格从低到高</option>
                <option>价格从高到低</option>
                <option>销量优先</option>
                <option>最新上架</option>
            </select>
        </div>
        <div class="flex border border-gray-300 rounded-lg">
            <button class="px-3 py-2 bg-orange-primary text-white rounded-l-lg">
                <i class="fa fa-th"></i>
            </button>
            <button class="px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-r-lg">
                <i class="fa fa-list"></i>
            </button>
        </div>
    </div>

    <!-- 商品网格 -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {% for product in products %}
        <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
            <div class="relative">
                <a href="{% url 'products:detail' product.id %}">
                    <img src="{{ product.image }}" alt="{{ product.name }}" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                </a>
                {% if product.is_hot %}
                <div class="absolute top-3 left-3">
                    <span class="bg-orange-primary text-white text-xs font-medium py-1 px-2 rounded">热销</span>
                </div>
                {% endif %}
                {% if product.is_new %}
                <div class="absolute top-3 {% if product.is_hot %}left-16{% else %}left-3{% endif %}">
                    <span class="bg-green-500 text-white text-xs font-medium py-1 px-2 rounded">新品</span>
                </div>
                {% endif %}
                <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors" onclick="toggleFavorite({{ product.id }})">
                    <i class="fa fa-heart-o"></i>
                </button>
            </div>
            <div class="p-4">
                <div class="flex items-center mb-2">
                    <div class="flex text-yellow-400">
                        {% for i in "12345" %}
                            {% if forloop.counter <= product.rating|default:4 %}
                                <i class="fa fa-star"></i>
                            {% else %}
                                <i class="fa fa-star-o"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                    <span class="text-gray-500 text-sm ml-2">{{ product.rating }} ({{ product.reviews_count }})</span>
                </div>
                <h3 class="font-medium text-gray-800 mb-1">
                    <a href="{% url 'products:detail' product.id %}" class="hover:text-orange-primary transition-colors">
                        {{ product.name }}
                    </a>
                </h3>
                <p class="text-gray-500 text-sm mb-3">{{ product.description|truncatechars:50 }}</p>
                <div class="flex justify-between items-center">
                    <div>
                        <span class="text-orange-primary font-bold">¥{{ product.price }}</span>
                        {% if product.original_price and product.original_price > product.price %}
                        <span class="text-gray-400 line-through text-sm ml-2">¥{{ product.original_price }}</span>
                        {% endif %}
                    </div>
                    <button class="bg-orange-primary hover:bg-orange-secondary text-white w-10 h-10 rounded-full flex items-center justify-center transition-colors" onclick="addToCart({{ product.id }})">
                        <i class="fa fa-shopping-cart"></i>
                    </button>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-span-full text-center py-16">
            <i class="fa fa-shopping-bag text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">暂无商品</h3>
            <p class="text-gray-500">请稍后再来查看</p>
        </div>
        {% endfor %}
    </div>

    <!-- 分页 -->
    {% if products|length > 12 %}
    <div class="flex justify-center mt-8">
        <nav class="flex space-x-2">
            <button class="px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50" disabled>
                上一页
            </button>
            <button class="px-3 py-2 text-white bg-orange-primary border border-orange-primary rounded-lg">
                1
            </button>
            <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                2
            </button>
            <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                3
            </button>
            <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                下一页
            </button>
        </nav>
    </div>
    {% endif %}

    <!-- 推荐商品 -->
    <div class="mt-16">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">猜你喜欢</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- 推荐商品卡片 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                <div class="relative">
                    <a href="{% url 'products:detail' 5 %}">
                        <img src="https://picsum.photos/300/300?random=20" alt="推荐商品" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    </a>
                    <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                        <i class="fa fa-heart-o"></i>
                    </button>
                </div>
                <div class="p-4">
                    <h3 class="font-medium text-gray-800 mb-1">蓝牙键盘</h3>
                    <div class="flex justify-between items-center">
                        <span class="text-orange-primary font-bold">¥199</span>
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-shopping-cart text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                <div class="relative">
                    <a href="{% url 'products:detail' 6 %}">
                        <img src="https://picsum.photos/300/300?random=21" alt="推荐商品" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    </a>
                    <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                        <i class="fa fa-heart-o"></i>
                    </button>
                </div>
                <div class="p-4">
                    <h3 class="font-medium text-gray-800 mb-1">无线鼠标</h3>
                    <div class="flex justify-between items-center">
                        <span class="text-orange-primary font-bold">¥89</span>
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-shopping-cart text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                <div class="relative">
                    <a href="{% url 'products:detail' 7 %}">
                        <img src="https://picsum.photos/300/300?random=22" alt="推荐商品" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    </a>
                    <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                        <i class="fa fa-heart-o"></i>
                    </button>
                </div>
                <div class="p-4">
                    <h3 class="font-medium text-gray-800 mb-1">充电宝</h3>
                    <div class="flex justify-between items-center">
                        <span class="text-orange-primary font-bold">¥129</span>
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-shopping-cart text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                <div class="relative">
                    <a href="{% url 'products:detail' 8 %}">
                        <img src="https://picsum.photos/300/300?random=23" alt="推荐商品" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    </a>
                    <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                        <i class="fa fa-heart-o"></i>
                    </button>
                </div>
                <div class="p-4">
                    <h3 class="font-medium text-gray-800 mb-1">手机支架</h3>
                    <div class="flex justify-between items-center">
                        <span class="text-orange-primary font-bold">¥39</span>
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-shopping-cart text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleFavorite(productId) {
    if (!isLoggedIn()) {
        alert('请先登录');
        window.location.href = '{% url "users:login" %}';
        return;
    }
    alert('收藏功能');
}

function addToCart(productId) {
    if (!isLoggedIn()) {
        alert('请先登录');
        window.location.href = '{% url "users:login" %}';
        return;
    }
    alert('已添加到购物车');
}

function isLoggedIn() {
    return {% if user.is_authenticated %}true{% else %}false{% endif %};
}
</script>
{% endblock %}

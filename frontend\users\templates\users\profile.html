{% extends 'base.html' %}

{% block title %}个人中心 - 智能电商{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- 侧边栏 -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                <!-- 用户信息 -->
                <div class="p-6 bg-gradient-to-r from-orange-primary to-orange-secondary text-white">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                            <i class="fa fa-user text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="font-semibold">{{ user.username|default:"用户名" }}</h3>
                            <p class="text-white/80 text-sm">黄金会员</p>
                        </div>
                    </div>
                    <div class="mt-4 grid grid-cols-2 gap-4 text-center">
                        <div>
                            <div class="text-lg font-bold">¥1,280</div>
                            <div class="text-white/80 text-xs">账户余额</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold">8,520</div>
                            <div class="text-white/80 text-xs">积分</div>
                        </div>
                    </div>
                </div>

                <!-- 导航菜单 -->
                <div class="p-0">
                    <nav class="space-y-1">
                        <a href="#profile" class="flex items-center px-6 py-3 text-orange-primary bg-orange-50 border-r-2 border-orange-primary">
                            <i class="fa fa-user mr-3"></i>个人信息
                        </a>
                        <a href="#orders" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-orange-primary transition-colors">
                            <i class="fa fa-shopping-bag mr-3"></i>我的订单
                        </a>
                        <a href="#addresses" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-orange-primary transition-colors">
                            <i class="fa fa-map-marker mr-3"></i>收货地址
                        </a>
                        <a href="#favorites" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-orange-primary transition-colors">
                            <i class="fa fa-heart mr-3"></i>我的收藏
                        </a>
                        <a href="#coupons" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-orange-primary transition-colors">
                            <i class="fa fa-ticket mr-3"></i>优惠券
                        </a>
                        <a href="#security" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-orange-primary transition-colors">
                            <i class="fa fa-shield mr-3"></i>账户安全
                        </a>
                        <a href="{% url 'users:logout' %}" class="flex items-center px-6 py-3 text-red-600 hover:bg-red-50 transition-colors" onclick="return confirm('确定要退出登录吗？')">
                            <i class="fa fa-sign-out mr-3"></i>退出登录
                        </a>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="lg:col-span-3">
            <!-- 个人信息 -->
            <div id="profile" class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold text-gray-800">个人信息</h2>
                    <button class="text-orange-primary hover:text-orange-secondary" onclick="toggleEdit()">
                        <i class="fa fa-edit mr-1"></i>编辑
                    </button>
                </div>

                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                            <input type="text" value="{{ user.username|default:'testuser' }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-primary/30" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                            <input type="email" value="{{ user.email|default:'<EMAIL>' }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-primary/30" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
                            <input type="tel" value="138****8000" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-primary/30" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">性别</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-primary/30" disabled>
                                <option>男</option>
                                <option>女</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">生日</label>
                            <input type="date" value="1990-01-01" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-primary/30" readonly>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">注册时间</label>
                            <input type="text" value="2024-01-01" class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50" readonly>
                        </div>
                    </div>

                    <div class="hidden" id="editButtons">
                        <div class="flex space-x-4">
                            <button type="submit" class="bg-orange-primary hover:bg-orange-secondary text-white px-6 py-2 rounded-lg transition-colors">
                                保存修改
                            </button>
                            <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors" onclick="cancelEdit()">
                                取消
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- VIP会员信息 -->
            <div class="bg-white rounded-xl shadow-sm p-6 mt-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">会员信息</h3>
                <div class="bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-lg p-4 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-semibold">黄金会员</h4>
                            <p class="text-yellow-100 text-sm">享受95折优惠，免费配送</p>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold">¥8,520</div>
                            <div class="text-yellow-100 text-xs">累计消费</div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="flex justify-between text-sm mb-1">
                            <span>升级到钻石会员</span>
                            <span>¥11,480 / ¥20,000</span>
                        </div>
                        <div class="w-full bg-yellow-300 rounded-full h-2">
                            <div class="bg-white h-2 rounded-full" style="width: 57%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近订单 -->
            <div class="bg-white rounded-xl shadow-sm p-6 mt-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">最近订单</h3>
                    <a href="{% url 'orders:list' %}" class="text-orange-primary hover:text-orange-secondary text-sm">
                        查看全部 <i class="fa fa-angle-right ml-1"></i>
                    </a>
                </div>

                <div class="space-y-4">
                    <!-- 订单1 -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <span class="text-sm text-gray-500">订单号：ORD202401001</span>
                                <span class="ml-4 bg-green-100 text-green-800 text-xs px-2 py-1 rounded">已完成</span>
                            </div>
                            <span class="text-sm text-gray-500">2024-01-15</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <img src="https://picsum.photos/60/60?random=10" alt="商品" class="w-12 h-12 object-cover rounded">
                            <div class="flex-1">
                                <h4 class="text-sm font-medium text-gray-800">智能手表 Pro</h4>
                                <p class="text-xs text-gray-500">等2件商品</p>
                            </div>
                            <div class="text-right">
                                <div class="text-orange-primary font-bold">¥2,198</div>
                                <button class="text-orange-primary hover:text-orange-secondary text-xs mt-1">再次购买</button>
                            </div>
                        </div>
                    </div>

                    <!-- 订单2 -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <span class="text-sm text-gray-500">订单号：ORD202401002</span>
                                <span class="ml-4 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">配送中</span>
                            </div>
                            <span class="text-sm text-gray-500">2024-01-18</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <img src="https://picsum.photos/60/60?random=11" alt="商品" class="w-12 h-12 object-cover rounded">
                            <div class="flex-1">
                                <h4 class="text-sm font-medium text-gray-800">无线降噪耳机</h4>
                                <p class="text-xs text-gray-500">等1件商品</p>
                            </div>
                            <div class="text-right">
                                <div class="text-orange-primary font-bold">¥899</div>
                                <button class="text-orange-primary hover:text-orange-secondary text-xs mt-1">查看物流</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 收货地址 -->
            <div class="bg-white rounded-xl shadow-sm p-6 mt-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">收货地址</h3>
                    <button class="text-orange-primary hover:text-orange-secondary text-sm" onclick="addAddress()">
                        <i class="fa fa-plus mr-1"></i>添加地址
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- 地址1 -->
                    <div class="border border-gray-200 rounded-lg p-4 relative">
                        <div class="absolute top-3 right-3">
                            <span class="bg-orange-primary text-white text-xs px-2 py-1 rounded">默认</span>
                        </div>
                        <div class="pr-12">
                            <div class="font-medium text-gray-800">张三</div>
                            <div class="text-sm text-gray-600 mt-1">138****8000</div>
                            <div class="text-sm text-gray-600 mt-1">北京市朝阳区xxx街道xxx号xxx小区xxx楼xxx室</div>
                        </div>
                        <div class="flex space-x-4 mt-3 text-sm">
                            <button class="text-orange-primary hover:text-orange-secondary">编辑</button>
                            <button class="text-red-500 hover:text-red-700">删除</button>
                        </div>
                    </div>

                    <!-- 地址2 -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="font-medium text-gray-800">李四</div>
                        <div class="text-sm text-gray-600 mt-1">139****9000</div>
                        <div class="text-sm text-gray-600 mt-1">上海市浦东新区xxx路xxx号xxx大厦xxx层</div>
                        <div class="flex space-x-4 mt-3 text-sm">
                            <button class="text-orange-primary hover:text-orange-secondary">编辑</button>
                            <button class="text-orange-primary hover:text-orange-secondary">设为默认</button>
                            <button class="text-red-500 hover:text-red-700">删除</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleEdit() {
    const inputs = document.querySelectorAll('input:not([readonly]), select');
    const editButtons = document.getElementById('editButtons');
    
    inputs.forEach(input => {
        if (input.type !== 'text' || !input.readOnly) {
            input.readOnly = false;
            input.disabled = false;
            input.classList.remove('bg-gray-50');
        }
    });
    
    editButtons.classList.remove('hidden');
}

function cancelEdit() {
    const inputs = document.querySelectorAll('input, select');
    const editButtons = document.getElementById('editButtons');
    
    inputs.forEach(input => {
        if (input.type !== 'text' || input.value !== '2024-01-01') {
            input.readOnly = true;
            input.disabled = true;
        }
    });
    
    editButtons.classList.add('hidden');
}

function addAddress() {
    alert('添加地址功能需要连接到实际的数据库');
}

function changePassword() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (!currentPassword || !newPassword || !confirmPassword) {
        alert('请填写所有密码字段');
        return;
    }

    if (newPassword.length < 6) {
        alert('新密码长度至少6位');
        return;
    }

    if (newPassword !== confirmPassword) {
        alert('两次输入的新密码不一致');
        return;
    }

    // 发送AJAX请求到后端
    fetch('{% url "users:change_password" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            current_password: currentPassword,
            new_password: newPassword,
            confirm_password: confirmPassword
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('密码修改成功');
            // 关闭模态框
            document.getElementById('changePasswordModal').style.display = 'none';
            // 重置表单
            document.getElementById('changePasswordForm').reset();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('修改密码时发生错误');
    });
}

// 侧边栏导航
document.querySelectorAll('nav a').forEach(link => {
    link.addEventListener('click', function(e) {
        e.preventDefault();
        
        // 移除所有活动状态
        document.querySelectorAll('nav a').forEach(a => {
            a.classList.remove('text-orange-primary', 'bg-orange-50', 'border-r-2', 'border-orange-primary');
            a.classList.add('text-gray-700');
        });
        
        // 添加活动状态到当前链接
        this.classList.remove('text-gray-700');
        this.classList.add('text-orange-primary', 'bg-orange-50', 'border-r-2', 'border-orange-primary');
        
        // 这里可以添加显示对应内容的逻辑
        const target = this.getAttribute('href').substring(1);
        console.log('切换到:', target);
    });
});
</script>
{% endblock %}

{% extends 'admin_panel/base.html' %}

{% block title %}个人中心 - 智能电商管理后台{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-user-cog me-2"></i>个人中心</h1>
</div>

<div class="row">
    <div class="col-md-4">
        <!-- 个人信息卡片 -->
        <div class="card content-card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-user-circle fa-5x text-primary"></i>
                </div>
                <h5>{{ user.username }}</h5>
                <p class="text-muted">{{ admin_user.position|default:"管理员" }}</p>
                <p class="text-muted">{{ admin_user.department|default:"未设置部门" }}</p>
                
                <div class="row text-center mt-3">
                    <div class="col-6">
                        <div class="border-end">
                            <h6 class="text-primary">注册时间</h6>
                            <small>{{ user.date_joined|date:"Y-m-d" }}</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="text-primary">最后登录</h6>
                        <small>{{ user.last_login|date:"Y-m-d H:i"|default:"从未登录" }}</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 权限信息 -->
        <div class="card content-card mt-4">
            <div class="card-header">
                <h6><i class="fas fa-shield-alt me-2"></i>权限信息</h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    {% if user.is_superuser %}
                        超级管理员
                    {% elif user.is_staff %}
                        普通管理员
                    {% else %}
                        普通用户
                    {% endif %}
                </div>
                {% if user.is_superuser %}
                <div class="mb-2">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    系统管理权限
                </div>
                {% endif %}
                {% if user.is_staff %}
                <div class="mb-2">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    后台访问权限
                </div>
                {% endif %}
                <div class="mb-2">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    商品管理权限
                </div>
                <div class="mb-2">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    订单管理权限
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- 编辑个人信息 -->
        <div class="card content-card">
            <div class="card-header">
                <h6><i class="fas fa-edit me-2"></i>编辑个人信息</h6>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" value="{{ user.username }}" readonly>
                                <div class="form-text">用户名不可修改</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">姓</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" value="{{ user.first_name }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">名</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" value="{{ user.last_name }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">手机号</label>
                                <input type="text" class="form-control" id="phone" name="phone" value="{{ admin_user.phone }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department" class="form-label">部门</label>
                                <input type="text" class="form-control" id="department" name="department" value="{{ admin_user.department }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="position" class="form-label">职位</label>
                        <input type="text" class="form-control" id="position" name="position" value="{{ admin_user.position }}">
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>保存修改
                        </button>
                        <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                            <i class="fas fa-key me-2"></i>修改密码
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 操作统计 -->
        <div class="card content-card mt-4">
            <div class="card-header">
                <h6><i class="fas fa-chart-bar me-2"></i>操作统计</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-primary">0</h4>
                            <small>今日操作</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-success">0</h4>
                            <small>本周操作</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-warning">0</h4>
                            <small>本月操作</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-info">0</h4>
                        <small>总操作数</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 修改密码模态框 -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-key me-2"></i>修改密码</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="mb-3">
                        <label for="currentPassword" class="form-label">当前密码</label>
                        <input type="password" class="form-control" id="currentPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">新密码</label>
                        <input type="password" class="form-control" id="newPassword" required>
                        <div class="form-text">密码长度至少6位</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">确认新密码</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="changePassword()">
                    <i class="fas fa-save me-2"></i>修改密码
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function changePassword() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (!currentPassword || !newPassword || !confirmPassword) {
        alert('请填写所有密码字段');
        return;
    }
    
    if (newPassword.length < 6) {
        alert('新密码长度至少6位');
        return;
    }
    
    if (newPassword !== confirmPassword) {
        alert('两次输入的新密码不一致');
        return;
    }
    
    // 这里应该发送AJAX请求到后端
    alert('密码修改功能需要连接到实际的数据库');
    
    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('changePasswordModal'));
    modal.hide();
    
    // 重置表单
    document.getElementById('changePasswordForm').reset();
}
</script>
{% endblock %}

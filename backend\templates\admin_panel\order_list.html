{% extends 'admin_panel/base.html' %}

{% block title %}订单管理 - 智能电商管理后台{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-shopping-cart me-2"></i>订单管理</h1>
</div>

<!-- 搜索和筛选 -->
<div class="card content-card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <input type="text" class="form-control" placeholder="搜索订单号或用户名">
            </div>
            <div class="col-md-2">
                <select class="form-select">
                    <option value="">所有状态</option>
                    <option value="pending">待付款</option>
                    <option value="paid">已付款</option>
                    <option value="shipped">已发货</option>
                    <option value="delivered">已送达</option>
                    <option value="completed">已完成</option>
                    <option value="cancelled">已取消</option>
                    <option value="refunded">已退款</option>
                </select>
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" placeholder="开始日期">
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" placeholder="结束日期">
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-primary">
                    <i class="fas fa-search me-2"></i>搜索
                </button>
            </div>
        </div>
    </div>
</div>

<div class="card content-card">
    <div class="card-header">
        <h5><i class="fas fa-list me-2"></i>订单列表</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>订单号</th>
                        <th>用户信息</th>
                        <th>商品信息</th>
                        <th>订单金额</th>
                        <th>支付方式</th>
                        <th>订单状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <strong>ORD202401001</strong>
                        </td>
                        <td>
                            <div>
                                <strong>testuser</strong>
                                <br>
                                <small class="text-muted"><EMAIL></small>
                                <br>
                                <small class="text-muted">13800138000</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <small>iPhone 15 Pro Max × 1</small>
                                <br>
                                <small class="text-muted">共1件商品</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong class="text-danger">¥9999.00</strong>
                                <br>
                                <small class="text-muted">含运费: ¥0.00</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-primary">支付宝</span>
                        </td>
                        <td>
                            <span class="badge bg-success">已付款</span>
                        </td>
                        <td>2024-01-01<br>10:30</td>
                        <td>
                            <button class="btn btn-sm btn-outline-info me-1" title="查看详情" onclick="viewOrder('ORD202401001')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success me-1" title="发货" onclick="shipOrder('ORD202401001')">
                                <i class="fas fa-truck"></i>
                            </button>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                    更多
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="printOrder('ORD202401001')">
                                        <i class="fas fa-print me-2"></i>打印订单
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="cancelOrder('ORD202401001')">
                                        <i class="fas fa-times me-2"></i>取消订单
                                    </a></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong>ORD202401002</strong>
                        </td>
                        <td>
                            <div>
                                <strong>vipuser</strong>
                                <br>
                                <small class="text-muted"><EMAIL></small>
                                <br>
                                <small class="text-muted">13900139000</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <small>小米14 Ultra × 1</small>
                                <br>
                                <small>MacBook Pro × 1</small>
                                <br>
                                <small class="text-muted">共2件商品</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong class="text-danger">¥31998.00</strong>
                                <br>
                                <small class="text-success">优惠: -¥200.00</small>
                                <br>
                                <small class="text-muted">含运费: ¥0.00</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-success">微信支付</span>
                        </td>
                        <td>
                            <span class="badge bg-info">已发货</span>
                        </td>
                        <td>2024-01-02<br>14:20</td>
                        <td>
                            <button class="btn btn-sm btn-outline-info me-1" title="查看详情" onclick="viewOrder('ORD202401002')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning me-1" title="物流跟踪" onclick="trackOrder('ORD202401002')">
                                <i class="fas fa-map-marker-alt"></i>
                            </button>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                    更多
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="printOrder('ORD202401002')">
                                        <i class="fas fa-print me-2"></i>打印订单
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="confirmDelivery('ORD202401002')">
                                        <i class="fas fa-check me-2"></i>确认送达
                                    </a></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong>ORD202401003</strong>
                        </td>
                        <td>
                            <div>
                                <strong>newuser</strong>
                                <br>
                                <small class="text-muted"><EMAIL></small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <small>戴森V15吸尘器 × 1</small>
                                <br>
                                <small class="text-muted">共1件商品</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong class="text-danger">¥3990.00</strong>
                                <br>
                                <small class="text-muted">含运费: ¥15.00</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-warning">余额支付</span>
                        </td>
                        <td>
                            <span class="badge bg-secondary">待付款</span>
                        </td>
                        <td>2024-01-03<br>09:15</td>
                        <td>
                            <button class="btn btn-sm btn-outline-info me-1" title="查看详情" onclick="viewOrder('ORD202401003')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger me-1" title="取消订单" onclick="cancelOrder('ORD202401003')">
                                <i class="fas fa-times"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <nav aria-label="订单分页">
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <span class="page-link">上一页</span>
                </li>
                <li class="page-item active">
                    <span class="page-link">1</span>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">2</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">3</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">下一页</a>
                </li>
            </ul>
        </nav>
    </div>
</div>

<!-- 订单详情模态框 -->
<div class="modal fade" id="orderDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-shopping-cart me-2"></i>订单详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>订单信息</h6>
                        <table class="table table-sm">
                            <tr>
                                <td>订单号:</td>
                                <td id="detailOrderNo">-</td>
                            </tr>
                            <tr>
                                <td>订单状态:</td>
                                <td id="detailStatus">-</td>
                            </tr>
                            <tr>
                                <td>支付方式:</td>
                                <td id="detailPayment">-</td>
                            </tr>
                            <tr>
                                <td>创建时间:</td>
                                <td id="detailCreateTime">-</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>收货信息</h6>
                        <table class="table table-sm">
                            <tr>
                                <td>收货人:</td>
                                <td id="detailReceiver">-</td>
                            </tr>
                            <tr>
                                <td>联系电话:</td>
                                <td id="detailPhone">-</td>
                            </tr>
                            <tr>
                                <td>收货地址:</td>
                                <td id="detailAddress">-</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <h6 class="mt-3">商品清单</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>单价</th>
                                <th>数量</th>
                                <th>小计</th>
                            </tr>
                        </thead>
                        <tbody id="orderItems">
                            <tr>
                                <td colspan="4" class="text-center text-muted">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>订单备注</h6>
                        <p id="orderRemark" class="text-muted">无备注</p>
                    </div>
                    <div class="col-md-6">
                        <h6>费用明细</h6>
                        <table class="table table-sm">
                            <tr>
                                <td>商品总额:</td>
                                <td id="detailSubtotal">-</td>
                            </tr>
                            <tr>
                                <td>运费:</td>
                                <td id="detailShipping">-</td>
                            </tr>
                            <tr>
                                <td>优惠金额:</td>
                                <td id="detailDiscount">-</td>
                            </tr>
                            <tr class="table-warning">
                                <td><strong>订单总额:</strong></td>
                                <td><strong id="detailTotal">-</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="printOrder()">
                    <i class="fas fa-print me-2"></i>打印订单
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewOrder(orderNo) {
    // 这里应该发送AJAX请求获取订单详情
    alert('查看订单详情功能需要连接到实际的数据库');
    
    // 示例数据
    document.getElementById('detailOrderNo').textContent = orderNo;
    document.getElementById('detailStatus').innerHTML = '<span class="badge bg-success">已付款</span>';
    document.getElementById('detailPayment').textContent = '支付宝';
    document.getElementById('detailCreateTime').textContent = '2024-01-01 10:30:00';
    document.getElementById('detailReceiver').textContent = '张三';
    document.getElementById('detailPhone').textContent = '13800138000';
    document.getElementById('detailAddress').textContent = '北京市朝阳区xxx街道xxx号';
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('orderDetailModal'));
    modal.show();
}

function shipOrder(orderNo) {
    if (confirm('确定要发货吗？')) {
        alert('发货功能需要连接到实际的数据库');
    }
}

function cancelOrder(orderNo) {
    if (confirm('确定要取消这个订单吗？')) {
        alert('取消订单功能需要连接到实际的数据库');
    }
}

function trackOrder(orderNo) {
    alert('物流跟踪功能需要连接到实际的物流API');
}

function printOrder(orderNo) {
    alert('打印订单功能需要连接到实际的打印服务');
}

function confirmDelivery(orderNo) {
    if (confirm('确定订单已送达吗？')) {
        alert('确认送达功能需要连接到实际的数据库');
    }
}
</script>
{% endblock %}

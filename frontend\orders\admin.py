from django.contrib import admin
from .models import DiscountRule, Order, OrderItem, OrderStatusLog


@admin.register(DiscountRule)
class DiscountRuleAdmin(admin.ModelAdmin):
    list_display = ['name', 'min_amount', 'discount_amount', 'discount_percentage', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name']
    list_editable = ['is_active']


class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0
    readonly_fields = ['total_price']


class OrderStatusLogInline(admin.TabularInline):
    model = OrderStatusLog
    extra = 0
    readonly_fields = ['created_at']


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = [
        'order_no', 'user', 'status', 'total_amount', 'final_amount', 
        'receiver_name', 'created_at'
    ]
    list_filter = ['status', 'created_at']
    search_fields = ['order_no', 'user__username', 'receiver_name', 'receiver_phone']
    readonly_fields = ['order_no', 'created_at']
    inlines = [OrderItemInline, OrderStatusLogInline]
    raw_id_fields = ['user']
    
    fieldsets = (
        ('订单信息', {
            'fields': ('order_no', 'user', 'status', 'note')
        }),
        ('金额信息', {
            'fields': ('total_amount', 'discount_amount', 'shipping_fee', 'final_amount', 'discount_rule')
        }),
        ('收货信息', {
            'fields': (
                'receiver_name', 'receiver_phone', 'receiver_province', 
                'receiver_city', 'receiver_district', 'receiver_address', 'receiver_postal_code'
            )
        }),
        ('物流信息', {
            'fields': ('shipping_company', 'tracking_number')
        }),
        ('时间信息', {
            'fields': (
                'created_at', 'paid_at', 'shipped_at', 
                'delivered_at', 'completed_at', 'cancelled_at'
            )
        }),
    )


@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    list_display = ['order', 'product_name', 'quantity', 'price', 'total_price']
    list_filter = ['order__created_at']
    search_fields = ['order__order_no', 'product_name', 'product_sku']
    raw_id_fields = ['order', 'product']

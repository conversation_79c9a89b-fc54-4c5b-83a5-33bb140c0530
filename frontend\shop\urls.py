from django.urls import path
from . import views

app_name = 'shop'

urlpatterns = [
    # 首页
    path('', views.index, name='index'),
    
    # 商品
    path('products/', views.product_list, name='product_list'),
    path('product/<int:product_id>/', views.product_detail, name='product_detail'),
    
    # 用户认证
    path('register/', views.user_register, name='register'),
    path('login/', views.user_login, name='login'),
    path('logout/', views.user_logout, name='logout'),
    
    # 用户中心
    path('profile/', views.user_profile, name='profile'),
    
    # 购物车
    path('cart/', views.cart_view, name='cart'),
    path('add-to-cart/', views.add_to_cart, name='add_to_cart'),
    
    # 订单
    path('orders/', views.order_list, name='order_list'),
    path('order/<int:order_id>/', views.order_detail, name='order_detail'),
    path('create-order/', views.create_order, name='create_order'),
]

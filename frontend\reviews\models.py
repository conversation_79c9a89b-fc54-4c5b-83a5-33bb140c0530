from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from products.models import Product
from orders.models import Order


class Review(models.Model):
    """商品评价"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name='商品')
    order = models.ForeignKey(Order, on_delete=models.CASCADE, verbose_name='订单')
    rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)], 
        verbose_name='评分'
    )
    content = models.TextField(verbose_name='评价内容')
    is_anonymous = models.BooleanField(default=False, verbose_name='匿名评价')
    is_approved = models.BooleanField(default=True, verbose_name='是否审核通过')
    admin_reply = models.TextField(null=True, blank=True, verbose_name='管理员回复')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='评价时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '商品评价'
        verbose_name_plural = '商品评价'
        unique_together = ['user', 'product', 'order']
        ordering = ['-created_at']

    def __str__(self):
        username = '匿名用户' if self.is_anonymous else self.user.username
        return f"{username} - {self.product.name} - {self.rating}星"

    @property
    def display_username(self):
        """显示用户名"""
        if self.is_anonymous:
            return '匿名用户'
        return self.user.username


class ReviewImage(models.Model):
    """评价图片"""
    review = models.ForeignKey(Review, on_delete=models.CASCADE, related_name='images', verbose_name='评价')
    image = models.ImageField(upload_to='reviews/', verbose_name='图片')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='上传时间')

    class Meta:
        verbose_name = '评价图片'
        verbose_name_plural = '评价图片'

    def __str__(self):
        return f"{self.review.product.name} - 评价图片"

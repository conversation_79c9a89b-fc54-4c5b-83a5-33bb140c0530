from django.db import models
from django.contrib.auth.models import User
from orders.models import Order


class Payment(models.Model):
    """支付记录"""
    PAYMENT_METHOD_CHOICES = [
        ('alipay', '支付宝'),
        ('wechat', '微信支付'),
        ('balance', '余额支付'),
        ('bank', '银行卡'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待支付'),
        ('processing', '支付中'),
        ('success', '支付成功'),
        ('failed', '支付失败'),
        ('cancelled', '已取消'),
        ('refunded', '已退款'),
    ]

    payment_no = models.CharField(max_length=50, unique=True, verbose_name='支付单号')
    order = models.ForeignKey(Order, on_delete=models.CASCADE, verbose_name='订单')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, verbose_name='支付方式')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='支付金额')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='支付状态')
    
    # 第三方支付信息
    third_party_no = models.CharField(max_length=100, null=True, blank=True, verbose_name='第三方支付单号')
    third_party_response = models.TextField(null=True, blank=True, verbose_name='第三方响应')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    paid_at = models.DateTimeField(null=True, blank=True, verbose_name='支付时间')

    class Meta:
        verbose_name = '支付记录'
        verbose_name_plural = '支付记录'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.payment_no} - {self.order.order_no}"


class PaymentLog(models.Model):
    """支付日志"""
    payment = models.ForeignKey(Payment, on_delete=models.CASCADE, verbose_name='支付记录')
    action = models.CharField(max_length=50, verbose_name='操作')
    request_data = models.TextField(null=True, blank=True, verbose_name='请求数据')
    response_data = models.TextField(null=True, blank=True, verbose_name='响应数据')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '支付日志'
        verbose_name_plural = '支付日志'
        ordering = ['-created_at']

{% extends 'base.html' %}

{% block title %}用户注册 - 智能电商{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-orange-100">
                <i class="fa fa-user-plus text-orange-primary text-xl"></i>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                创建新账户
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                已有账户？
                <a href="{% url 'users:login' %}" class="font-medium text-orange-primary hover:text-orange-secondary">
                    立即登录
                </a>
            </p>
        </div>
        
        <form class="mt-8 space-y-6" method="post">
            {% csrf_token %}
            <div class="space-y-4">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700">用户名</label>
                    <input id="username" name="username" type="text" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-orange-primary focus:border-orange-primary sm:text-sm" 
                           placeholder="请输入用户名">
                </div>
                
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">邮箱</label>
                    <input id="email" name="email" type="email" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-orange-primary focus:border-orange-primary sm:text-sm" 
                           placeholder="请输入邮箱地址">
                </div>
                
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">密码</label>
                    <input id="password" name="password" type="password" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-orange-primary focus:border-orange-primary sm:text-sm" 
                           placeholder="请输入密码（至少6位）">
                </div>
                
                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700">确认密码</label>
                    <input id="confirm_password" name="confirm_password" type="password" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-orange-primary focus:border-orange-primary sm:text-sm" 
                           placeholder="请再次输入密码">
                </div>
            </div>

            <div class="flex items-center">
                <input id="agree-terms" name="agree-terms" type="checkbox" required
                       class="h-4 w-4 text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                <label for="agree-terms" class="ml-2 block text-sm text-gray-900">
                    我同意
                    <a href="#" class="text-orange-primary hover:text-orange-secondary">用户协议</a>
                    和
                    <a href="#" class="text-orange-primary hover:text-orange-secondary">隐私政策</a>
                </label>
            </div>

            <div>
                <button type="submit" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-orange-primary hover:bg-orange-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-primary">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fa fa-user-plus text-orange-secondary group-hover:text-orange-primary"></i>
                    </span>
                    注册账户
                </button>
            </div>

            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-gray-50 text-gray-500">或者使用第三方账号注册</span>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-2 gap-3">
                    <a href="#" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i class="fa fa-wechat text-green-500"></i>
                        <span class="ml-2">微信</span>
                    </a>
                    <a href="#" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i class="fa fa-qq text-blue-500"></i>
                        <span class="ml-2">QQ</span>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    // 密码确认验证
    confirmPassword.addEventListener('input', function() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('两次输入的密码不一致');
        } else {
            confirmPassword.setCustomValidity('');
        }
    });
    
    form.addEventListener('submit', function(e) {
        const username = document.getElementById('username').value;
        const email = document.getElementById('email').value;
        const pwd = password.value;
        const confirmPwd = confirmPassword.value;
        const agreeTerms = document.getElementById('agree-terms').checked;
        
        if (!username.trim()) {
            alert('请输入用户名');
            e.preventDefault();
            return;
        }
        
        if (!email.trim()) {
            alert('请输入邮箱');
            e.preventDefault();
            return;
        }
        
        if (pwd.length < 6) {
            alert('密码长度至少6位');
            e.preventDefault();
            return;
        }
        
        if (pwd !== confirmPwd) {
            alert('两次输入的密码不一致');
            e.preventDefault();
            return;
        }
        
        if (!agreeTerms) {
            alert('请同意用户协议和隐私政策');
            e.preventDefault();
            return;
        }
    });
});
</script>
{% endblock %}

{% extends 'base.html' %}

{% block title %}商品分类 - 智能电商{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- 页面标题 -->
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">商品分类</h1>
        <p class="text-gray-600 text-lg">精选优质商品，分类清晰，购物更便捷</p>
    </div>

    <!-- 分类展示 -->
    <div class="space-y-12">
        {% for category_group in categories_with_children %}
        <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
            <!-- 分类标题 -->
            <div class="bg-gradient-to-r from-orange-primary to-orange-secondary text-white p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-2xl font-bold">{{ category_group.parent.name }}</h2>
                        <p class="text-orange-100 mt-1">{{ category_group.parent.description }}</p>
                    </div>
                    <div class="text-right">
                        <a href="{% url 'products:category_detail' category_group.parent.id %}" class="bg-white/20 hover:bg-white/30 text-white px-6 py-2 rounded-lg transition-colors">
                            查看全部 <i class="fa fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 子分类 -->
            <div class="p-6">
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    {% for child in category_group.children %}
                    <a href="{% url 'products:search' %}?category={{ child.id }}" class="group">
                        <div class="bg-gray-50 rounded-xl p-6 text-center hover:shadow-md hover:bg-orange-50 transition-all duration-300">
                            <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-orange-200 transition-colors">
                                {% if '手机' in child.name %}
                                    <i class="fa fa-mobile text-orange-primary text-2xl"></i>
                                {% elif '电脑' in child.name %}
                                    <i class="fa fa-laptop text-orange-primary text-2xl"></i>
                                {% elif '数码' in child.name %}
                                    <i class="fa fa-headphones text-orange-primary text-2xl"></i>
                                {% elif '男装' in child.name %}
                                    <i class="fa fa-male text-orange-primary text-2xl"></i>
                                {% elif '女装' in child.name %}
                                    <i class="fa fa-female text-orange-primary text-2xl"></i>
                                {% elif '鞋' in child.name %}
                                    <i class="fa fa-shoe-prints text-orange-primary text-2xl"></i>
                                {% elif '家具' in child.name %}
                                    <i class="fa fa-couch text-orange-primary text-2xl"></i>
                                {% elif '家纺' in child.name %}
                                    <i class="fa fa-bed text-orange-primary text-2xl"></i>
                                {% elif '厨具' in child.name %}
                                    <i class="fa fa-utensils text-orange-primary text-2xl"></i>
                                {% else %}
                                    <i class="fa fa-cube text-orange-primary text-2xl"></i>
                                {% endif %}
                            </div>
                            <h3 class="font-semibold text-gray-800 group-hover:text-orange-primary transition-colors">{{ child.name }}</h3>
                            <p class="text-gray-500 text-sm mt-1">{{ child.description }}</p>
                        </div>
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- 热门推荐 -->
    <div class="mt-16">
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">热门推荐</h2>
            <p class="text-gray-600">精选热销商品，品质保证</p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- 推荐商品1 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                <div class="relative">
                    <a href="{% url 'products:detail' 1 %}">
                        <img src="https://picsum.photos/300/300?random=10" alt="智能手表" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    </a>
                    <div class="absolute top-3 left-3">
                        <span class="bg-orange-primary text-white text-xs font-medium py-1 px-2 rounded">热销</span>
                    </div>
                    <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                        <i class="fa fa-heart-o"></i>
                    </button>
                </div>
                <div class="p-4">
                    <h3 class="font-medium text-gray-800 mb-1">智能手表 Pro</h3>
                    <div class="flex justify-between items-center">
                        <span class="text-orange-primary font-bold">¥1,299</span>
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-shopping-cart text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 推荐商品2 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                <div class="relative">
                    <a href="{% url 'products:detail' 2 %}">
                        <img src="https://picsum.photos/300/300?random=11" alt="无线耳机" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    </a>
                    <div class="absolute top-3 left-3">
                        <span class="bg-green-500 text-white text-xs font-medium py-1 px-2 rounded">新品</span>
                    </div>
                    <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                        <i class="fa fa-heart-o"></i>
                    </button>
                </div>
                <div class="p-4">
                    <h3 class="font-medium text-gray-800 mb-1">无线降噪耳机</h3>
                    <div class="flex justify-between items-center">
                        <span class="text-orange-primary font-bold">¥899</span>
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-shopping-cart text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 推荐商品3 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                <div class="relative">
                    <a href="{% url 'products:detail' 3 %}">
                        <img src="https://picsum.photos/300/300?random=12" alt="智能音箱" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    </a>
                    <div class="absolute top-3 left-3">
                        <span class="bg-orange-primary text-white text-xs font-medium py-1 px-2 rounded">热销</span>
                    </div>
                    <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                        <i class="fa fa-heart-o"></i>
                    </button>
                </div>
                <div class="p-4">
                    <h3 class="font-medium text-gray-800 mb-1">智能音箱</h3>
                    <div class="flex justify-between items-center">
                        <span class="text-orange-primary font-bold">¥399</span>
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-shopping-cart text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 推荐商品4 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                <div class="relative">
                    <a href="{% url 'products:detail' 4 %}">
                        <img src="https://picsum.photos/300/300?random=13" alt="运动背包" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    </a>
                    <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                        <i class="fa fa-heart-o"></i>
                    </button>
                </div>
                <div class="p-4">
                    <h3 class="font-medium text-gray-800 mb-1">运动背包</h3>
                    <div class="flex justify-between items-center">
                        <span class="text-orange-primary font-bold">¥299</span>
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-shopping-cart text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 品牌推荐 -->
    <div class="mt-16">
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">热门品牌</h2>
            <p class="text-gray-600">知名品牌，品质保证</p>
        </div>

        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
            <a href="{% url 'products:search' %}?brand=1" class="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fa fa-apple text-gray-600 text-2xl"></i>
                </div>
                <h3 class="font-medium text-gray-800">苹果</h3>
            </a>
            <a href="{% url 'products:search' %}?brand=2" class="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fa fa-mobile text-gray-600 text-2xl"></i>
                </div>
                <h3 class="font-medium text-gray-800">小米</h3>
            </a>
            <a href="{% url 'products:search' %}?brand=3" class="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fa fa-laptop text-gray-600 text-2xl"></i>
                </div>
                <h3 class="font-medium text-gray-800">华为</h3>
            </a>
            <a href="{% url 'products:search' %}?brand=4" class="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fa fa-tv text-gray-600 text-2xl"></i>
                </div>
                <h3 class="font-medium text-gray-800">三星</h3>
            </a>
            <a href="{% url 'products:search' %}?brand=5" class="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fa fa-headphones text-gray-600 text-2xl"></i>
                </div>
                <h3 class="font-medium text-gray-800">索尼</h3>
            </a>
            <a href="{% url 'products:search' %}?brand=6" class="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fa fa-gamepad text-gray-600 text-2xl"></i>
                </div>
                <h3 class="font-medium text-gray-800">任天堂</h3>
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function addToCart(productId) {
    if (!isLoggedIn()) {
        alert('请先登录');
        window.location.href = '{% url "users:login" %}';
        return;
    }
    alert('已添加到购物车');
}

function isLoggedIn() {
    return {% if user.is_authenticated %}true{% else %}false{% endif %};
}
</script>
{% endblock %}

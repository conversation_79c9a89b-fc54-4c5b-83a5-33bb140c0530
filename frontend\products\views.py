from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg
from django.views.decorators.csrf import csrf_exempt
import json

from .models import Product, Category, Brand, Favorite, Banner, ProductImage


def index(request):
    """首页"""
    # 轮播图
    banners = Banner.objects.filter(is_active=True)[:5]
    
    # 热门商品
    hot_products = Product.objects.filter(is_hot=True, is_active=True)[:6]
    
    # 新品上市
    new_products = Product.objects.filter(is_new=True, is_active=True)[:8]
    
    # 推荐商品
    recommend_products = Product.objects.filter(is_recommend=True, is_active=True)[:4]
    
    # 主分类（父分类）
    main_categories = Category.objects.filter(parent=None, is_active=True)[:10]
    
    context = {
        'banners': banners,
        'hot_products': hot_products,
        'new_products': new_products,
        'recommend_products': recommend_products,
        'main_categories': main_categories,
    }
    return render(request, 'products/index.html', context)


def product_list(request):
    """商品列表"""
    # 使用静态数据
    products_data = [
        {
            'id': 1,
            'name': '智能手表 Pro',
            'description': '健康监测、运动追踪、消息提醒',
            'price': 1299.00,
            'original_price': 1599.00,
            'rating': 4.5,
            'reviews_count': 128,
            'is_hot': True,
            'is_new': False,
            'image': 'https://picsum.photos/400/400?random=10'
        },
        {
            'id': 2,
            'name': '无线降噪耳机',
            'description': '主动降噪，高清音质，舒适佩戴',
            'price': 899.00,
            'original_price': 1099.00,
            'rating': 4.0,
            'reviews_count': 86,
            'is_hot': False,
            'is_new': True,
            'image': 'https://picsum.photos/400/400?random=11'
        },
        {
            'id': 3,
            'name': '智能音箱',
            'description': '语音助手，智能家居控制',
            'price': 399.00,
            'original_price': 499.00,
            'rating': 4.2,
            'reviews_count': 64,
            'is_hot': True,
            'is_new': False,
            'image': 'https://picsum.photos/400/400?random=12'
        },
        {
            'id': 4,
            'name': '运动背包',
            'description': '专业运动背包，大容量设计',
            'price': 299.00,
            'original_price': 399.00,
            'rating': 4.3,
            'reviews_count': 45,
            'is_hot': False,
            'is_new': False,
            'image': 'https://picsum.photos/400/400?random=13'
        }
    ]

    context = {
        'products': products_data,
        'total_count': len(products_data),
    }
    return render(request, 'products/list.html', context)


def product_detail(request, product_id):
    """商品详情"""
    # 暂时使用静态数据，因为数据库可能没有商品数据
    product_data = {
        'id': product_id,
        'name': '智能手表 Pro',
        'description': '健康监测、运动追踪、消息提醒，功能强大的智能手表',
        'price': 1299.00,
        'original_price': 1599.00,
        'rating': 4.5,
        'reviews_count': 128,
        'sales': 1234,
        'stock': 50,
        'is_hot': True,
        'is_new': True,
    }

    # 相关商品（静态数据）
    related_products = [
        {
            'id': 2,
            'name': '无线降噪耳机',
            'price': 899.00,
            'image': 'https://picsum.photos/300/300?random=30'
        },
        {
            'id': 3,
            'name': '智能音箱',
            'price': 399.00,
            'image': 'https://picsum.photos/300/300?random=31'
        }
    ]

    context = {
        'product': product_data,
        'related_products': related_products,
        'is_favorited': False,
    }
    return render(request, 'products/detail.html', context)


def category_list(request):
    """分类列表"""
    # 使用静态数据
    categories_with_children = [
        {
            'parent': {'id': 1, 'name': '电子产品', 'description': '各类电子设备'},
            'children': [
                {'id': 11, 'name': '手机通讯', 'description': '智能手机、配件'},
                {'id': 12, 'name': '电脑办公', 'description': '笔记本、台式机'},
                {'id': 13, 'name': '数码配件', 'description': '耳机、充电器'},
            ]
        },
        {
            'parent': {'id': 2, 'name': '服装鞋帽', 'description': '时尚服饰'},
            'children': [
                {'id': 21, 'name': '男装', 'description': '男士服装'},
                {'id': 22, 'name': '女装', 'description': '女士服装'},
                {'id': 23, 'name': '鞋靴', 'description': '各类鞋子'},
            ]
        },
        {
            'parent': {'id': 3, 'name': '家居用品', 'description': '家庭生活用品'},
            'children': [
                {'id': 31, 'name': '家具', 'description': '桌椅床柜'},
                {'id': 32, 'name': '家纺', 'description': '床上用品'},
                {'id': 33, 'name': '厨具', 'description': '厨房用品'},
            ]
        }
    ]

    context = {
        'categories_with_children': categories_with_children,
    }
    return render(request, 'products/category_list.html', context)


def category_detail(request, category_id):
    """分类详情页面"""
    # 使用静态数据
    categories_data = {
        1: {'id': 1, 'name': '电子产品', 'description': '各类电子设备'},
        2: {'id': 2, 'name': '服装鞋帽', 'description': '时尚服饰'},
        3: {'id': 3, 'name': '家居用品', 'description': '家庭生活用品'},
    }

    category = categories_data.get(category_id, {'id': category_id, 'name': '未知分类', 'description': ''})

    # 子分类数据
    subcategories_data = {
        1: [
            {'id': 11, 'name': '手机通讯'},
            {'id': 12, 'name': '电脑办公'},
            {'id': 13, 'name': '数码配件'},
        ],
        2: [
            {'id': 21, 'name': '男装'},
            {'id': 22, 'name': '女装'},
            {'id': 23, 'name': '鞋靴'},
        ],
        3: [
            {'id': 31, 'name': '家具'},
            {'id': 32, 'name': '家纺'},
            {'id': 33, 'name': '厨具'},
        ]
    }

    subcategories = subcategories_data.get(category_id, [])

    # 商品数据
    products_data = [
        {
            'id': 1,
            'name': '智能手表 Pro',
            'description': '健康监测、运动追踪、消息提醒',
            'price': 1299.00,
            'original_price': 1599.00,
            'rating': 4.5,
            'reviews_count': 128,
            'is_hot': True,
            'is_new': False,
            'image': 'https://picsum.photos/400/400?random=10'
        },
        {
            'id': 2,
            'name': '无线降噪耳机',
            'description': '主动降噪，高清音质，舒适佩戴',
            'price': 899.00,
            'original_price': 1099.00,
            'rating': 4.0,
            'reviews_count': 86,
            'is_hot': False,
            'is_new': True,
            'image': 'https://picsum.photos/400/400?random=11'
        }
    ]

    # 品牌数据
    brands = [
        {'id': 1, 'name': '苹果'},
        {'id': 2, 'name': '小米'},
        {'id': 3, 'name': '华为'},
    ]

    context = {
        'category': category,
        'subcategories': subcategories,
        'brands': brands,
        'products': products_data,
        'total_count': len(products_data),
    }
    return render(request, 'products/category.html', context)


def brand_list(request):
    """品牌列表"""
    brands = Brand.objects.filter(is_active=True)
    
    # 分页
    paginator = Paginator(brands, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
    }
    return render(request, 'products/brand_list.html', context)


@login_required
@csrf_exempt
def toggle_favorite(request):
    """切换收藏状态"""
    if request.method == 'POST':
        data = json.loads(request.body)
        product_id = data.get('product_id')
        
        try:
            product = Product.objects.get(id=product_id, is_active=True)
            favorite, created = Favorite.objects.get_or_create(
                user=request.user,
                product=product
            )
            
            if not created:
                # 已收藏，取消收藏
                favorite.delete()
                return JsonResponse({
                    'success': True, 
                    'favorited': False, 
                    'message': '取消收藏成功'
                })
            else:
                # 添加收藏
                return JsonResponse({
                    'success': True, 
                    'favorited': True, 
                    'message': '收藏成功'
                })
                
        except Product.DoesNotExist:
            return JsonResponse({'success': False, 'message': '商品不存在'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
def favorite_list(request):
    """收藏列表"""
    favorites = Favorite.objects.filter(user=request.user).select_related('product')
    
    # 分页
    paginator = Paginator(favorites, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
    }
    return render(request, 'products/favorite_list.html', context)


def search(request):
    """商品搜索"""
    query = request.GET.get('q', '')
    category_id = request.GET.get('category', '')
    brand_id = request.GET.get('brand', '')
    sort_by = request.GET.get('sort', 'default')

    products = Product.objects.filter(is_active=True)

    # 搜索关键词
    if query:
        products = products.filter(
            Q(name__icontains=query) |
            Q(description__icontains=query) |
            Q(sku__icontains=query)
        )

    # 分类筛选
    if category_id:
        try:
            category = Category.objects.get(id=category_id)
            products = products.filter(category=category)
        except Category.DoesNotExist:
            pass

    # 品牌筛选
    if brand_id:
        try:
            brand = Brand.objects.get(id=brand_id)
            products = products.filter(brand=brand)
        except Brand.DoesNotExist:
            pass

    # 排序
    if sort_by == 'price_asc':
        products = products.order_by('price')
    elif sort_by == 'price_desc':
        products = products.order_by('-price')
    elif sort_by == 'sales':
        products = products.order_by('-sales')
    elif sort_by == 'new':
        products = products.order_by('-created_at')
    else:
        products = products.order_by('-created_at')

    # 分页
    paginator = Paginator(products, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # 获取分类和品牌用于筛选
    categories = Category.objects.filter(is_active=True)
    brands = Brand.objects.filter(is_active=True)

    context = {
        'query': query,
        'page_obj': page_obj,
        'categories': categories,
        'brands': brands,
        'current_category': category_id,
        'current_brand': brand_id,
        'sort_by': sort_by,
        'total_count': paginator.count,
    }
    return render(request, 'products/search.html', context)


def search_suggestions(request):
    """搜索建议"""
    query = request.GET.get('q', '')
    suggestions = []

    if query and len(query) >= 2:
        # 搜索商品名称
        products = Product.objects.filter(
            name__icontains=query,
            is_active=True
        )[:10]

        suggestions = [{'name': product.name, 'id': product.id} for product in products]

    return JsonResponse({'suggestions': suggestions})

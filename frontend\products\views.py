from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg
from django.views.decorators.csrf import csrf_exempt
import json

from .models import Product, Category, Brand, Favorite, Banner, ProductImage


def index(request):
    """首页"""
    # 轮播图
    banners = Banner.objects.filter(is_active=True)[:5]
    
    # 热门商品
    hot_products = Product.objects.filter(is_hot=True, is_active=True)[:6]
    
    # 新品上市
    new_products = Product.objects.filter(is_new=True, is_active=True)[:8]
    
    # 推荐商品
    recommend_products = Product.objects.filter(is_recommend=True, is_active=True)[:4]
    
    # 主分类（父分类）
    main_categories = Category.objects.filter(parent=None, is_active=True)[:10]
    
    context = {
        'banners': banners,
        'hot_products': hot_products,
        'new_products': new_products,
        'recommend_products': recommend_products,
        'main_categories': main_categories,
    }
    return render(request, 'products/index.html', context)


def product_list(request):
    """商品列表"""
    products = Product.objects.filter(is_active=True)
    categories = Category.objects.filter(is_active=True)
    brands = Brand.objects.filter(is_active=True)
    
    # 分类筛选
    category_id = request.GET.get('category')
    if category_id:
        try:
            category = Category.objects.get(id=category_id, is_active=True)
            if category.parent:
                # 子分类
                products = products.filter(category=category)
            else:
                # 父分类，显示所有子分类的商品
                child_categories = category.children
                products = products.filter(category__in=child_categories)
        except Category.DoesNotExist:
            pass
    
    # 品牌筛选
    brand_id = request.GET.get('brand')
    if brand_id:
        try:
            brand = Brand.objects.get(id=brand_id, is_active=True)
            products = products.filter(brand=brand)
        except Brand.DoesNotExist:
            pass
    
    # 搜索
    search = request.GET.get('search')
    if search:
        products = products.filter(
            Q(name__icontains=search) |
            Q(description__icontains=search) |
            Q(sku__icontains=search)
        )
    
    # 价格筛选
    min_price = request.GET.get('min_price')
    max_price = request.GET.get('max_price')
    if min_price:
        try:
            products = products.filter(price__gte=float(min_price))
        except ValueError:
            pass
    if max_price:
        try:
            products = products.filter(price__lte=float(max_price))
        except ValueError:
            pass
    
    # 排序
    sort = request.GET.get('sort', 'default')
    if sort == 'price_asc':
        products = products.order_by('price')
    elif sort == 'price_desc':
        products = products.order_by('-price')
    elif sort == 'sales':
        products = products.order_by('-sales')
    elif sort == 'new':
        products = products.order_by('-created_at')
    elif sort == 'rating':
        # 按评分排序需要关联评价表
        products = products.order_by('-created_at')  # 暂时按创建时间排序
    
    # 分页
    paginator = Paginator(products, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'categories': categories,
        'brands': brands,
        'current_category': category_id,
        'current_brand': brand_id,
        'search': search,
        'sort': sort,
        'min_price': min_price,
        'max_price': max_price,
    }
    return render(request, 'products/product_list.html', context)


def product_detail(request, product_id):
    """商品详情"""
    product = get_object_or_404(Product, id=product_id, is_active=True)
    
    # 商品图片
    product_images = ProductImage.objects.filter(product=product, is_active=True)
    
    # 相关商品
    related_products = Product.objects.filter(
        category=product.category, 
        is_active=True
    ).exclude(id=product.id)[:4]
    
    # 同品牌商品
    brand_products = []
    if product.brand:
        brand_products = Product.objects.filter(
            brand=product.brand,
            is_active=True
        ).exclude(id=product.id)[:4]
    
    # 用户是否收藏
    is_favorited = False
    if request.user.is_authenticated:
        is_favorited = Favorite.objects.filter(
            user=request.user, 
            product=product
        ).exists()
    
    # 商品评价（如果评价系统存在）
    reviews = []
    try:
        from reviews.models import Review
        reviews = Review.objects.filter(product=product)[:10]
    except ImportError:
        pass
    
    context = {
        'product': product,
        'product_images': product_images,
        'related_products': related_products,
        'brand_products': brand_products,
        'reviews': reviews,
        'is_favorited': is_favorited,
    }
    return render(request, 'products/product_detail.html', context)


def category_list(request):
    """分类列表"""
    # 获取所有父分类及其子分类
    parent_categories = Category.objects.filter(parent=None, is_active=True)
    
    categories_with_children = []
    for parent in parent_categories:
        children = parent.children
        categories_with_children.append({
            'parent': parent,
            'children': children
        })
    
    context = {
        'categories_with_children': categories_with_children,
    }
    return render(request, 'products/category_list.html', context)


def brand_list(request):
    """品牌列表"""
    brands = Brand.objects.filter(is_active=True)
    
    # 分页
    paginator = Paginator(brands, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
    }
    return render(request, 'products/brand_list.html', context)


@login_required
@csrf_exempt
def toggle_favorite(request):
    """切换收藏状态"""
    if request.method == 'POST':
        data = json.loads(request.body)
        product_id = data.get('product_id')
        
        try:
            product = Product.objects.get(id=product_id, is_active=True)
            favorite, created = Favorite.objects.get_or_create(
                user=request.user,
                product=product
            )
            
            if not created:
                # 已收藏，取消收藏
                favorite.delete()
                return JsonResponse({
                    'success': True, 
                    'favorited': False, 
                    'message': '取消收藏成功'
                })
            else:
                # 添加收藏
                return JsonResponse({
                    'success': True, 
                    'favorited': True, 
                    'message': '收藏成功'
                })
                
        except Product.DoesNotExist:
            return JsonResponse({'success': False, 'message': '商品不存在'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
def favorite_list(request):
    """收藏列表"""
    favorites = Favorite.objects.filter(user=request.user).select_related('product')
    
    # 分页
    paginator = Paginator(favorites, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
    }
    return render(request, 'products/favorite_list.html', context)


def search_suggestions(request):
    """搜索建议"""
    query = request.GET.get('q', '')
    suggestions = []
    
    if query and len(query) >= 2:
        # 搜索商品名称
        products = Product.objects.filter(
            name__icontains=query,
            is_active=True
        )[:10]
        
        suggestions = [{'name': product.name, 'id': product.id} for product in products]
    
    return JsonResponse({'suggestions': suggestions})

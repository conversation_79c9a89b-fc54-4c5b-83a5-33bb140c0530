from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg
from django.views.decorators.csrf import csrf_exempt
import json

from .models import Product, Category, Brand, Favorite, Banner, ProductImage


def index(request):
    """首页"""
    # 轮播图
    banners = Banner.objects.filter(is_active=True)[:5]
    
    # 热门商品
    hot_products = Product.objects.filter(is_hot=True, is_active=True)[:6]
    
    # 新品上市
    new_products = Product.objects.filter(is_new=True, is_active=True)[:8]
    
    # 推荐商品
    recommend_products = Product.objects.filter(is_recommend=True, is_active=True)[:4]
    
    # 主分类（父分类）
    main_categories = Category.objects.filter(parent=None, is_active=True)[:10]
    
    context = {
        'banners': banners,
        'hot_products': hot_products,
        'new_products': new_products,
        'recommend_products': recommend_products,
        'main_categories': main_categories,
    }
    return render(request, 'products/index.html', context)


def product_list(request):
    """商品列表"""
    # 使用静态数据
    products_data = [
        {
            'id': 1,
            'name': '智能手表 Pro',
            'description': '健康监测、运动追踪、消息提醒',
            'price': 1299.00,
            'original_price': 1599.00,
            'rating': 4.5,
            'reviews_count': 128,
            'is_hot': True,
            'is_new': False,
            'image': 'https://picsum.photos/400/400?random=10'
        },
        {
            'id': 2,
            'name': '无线降噪耳机',
            'description': '主动降噪，高清音质，舒适佩戴',
            'price': 899.00,
            'original_price': 1099.00,
            'rating': 4.0,
            'reviews_count': 86,
            'is_hot': False,
            'is_new': True,
            'image': 'https://picsum.photos/400/400?random=11'
        },
        {
            'id': 3,
            'name': '智能音箱',
            'description': '语音助手，智能家居控制',
            'price': 399.00,
            'original_price': 499.00,
            'rating': 4.2,
            'reviews_count': 64,
            'is_hot': True,
            'is_new': False,
            'image': 'https://picsum.photos/400/400?random=12'
        },
        {
            'id': 4,
            'name': '运动背包',
            'description': '专业运动背包，大容量设计',
            'price': 299.00,
            'original_price': 399.00,
            'rating': 4.3,
            'reviews_count': 45,
            'is_hot': False,
            'is_new': False,
            'image': 'https://picsum.photos/400/400?random=13'
        }
    ]

    context = {
        'products': products_data,
        'total_count': len(products_data),
    }
    return render(request, 'products/list.html', context)


def product_detail(request, product_id):
    """商品详情"""
    # 暂时使用静态数据，因为数据库可能没有商品数据
    product_data = {
        'id': product_id,
        'name': '智能手表 Pro',
        'description': '健康监测、运动追踪、消息提醒，功能强大的智能手表',
        'price': 1299.00,
        'original_price': 1599.00,
        'rating': 4.5,
        'reviews_count': 128,
        'sales': 1234,
        'stock': 50,
        'is_hot': True,
        'is_new': True,
    }

    # 相关商品（静态数据）
    related_products = [
        {
            'id': 2,
            'name': '无线降噪耳机',
            'price': 899.00,
            'image': 'https://picsum.photos/300/300?random=30'
        },
        {
            'id': 3,
            'name': '智能音箱',
            'price': 399.00,
            'image': 'https://picsum.photos/300/300?random=31'
        }
    ]

    context = {
        'product': product_data,
        'related_products': related_products,
        'is_favorited': False,
    }
    return render(request, 'products/detail.html', context)


def category_list(request):
    """分类列表"""
    # 使用静态数据
    categories_with_children = [
        {
            'parent': {'id': 1, 'name': '电子产品', 'description': '各类电子设备'},
            'children': [
                {'id': 11, 'name': '手机通讯', 'description': '智能手机、配件'},
                {'id': 12, 'name': '电脑办公', 'description': '笔记本、台式机'},
                {'id': 13, 'name': '数码配件', 'description': '耳机、充电器'},
            ]
        },
        {
            'parent': {'id': 2, 'name': '服装鞋帽', 'description': '时尚服饰'},
            'children': [
                {'id': 21, 'name': '男装', 'description': '男士服装'},
                {'id': 22, 'name': '女装', 'description': '女士服装'},
                {'id': 23, 'name': '鞋靴', 'description': '各类鞋子'},
            ]
        },
        {
            'parent': {'id': 3, 'name': '家居用品', 'description': '家庭生活用品'},
            'children': [
                {'id': 31, 'name': '家具', 'description': '桌椅床柜'},
                {'id': 32, 'name': '家纺', 'description': '床上用品'},
                {'id': 33, 'name': '厨具', 'description': '厨房用品'},
            ]
        }
    ]

    context = {
        'categories_with_children': categories_with_children,
    }
    return render(request, 'products/category_list.html', context)


def category_detail(request, category_id):
    """分类详情页面"""
    # 使用静态数据
    categories_data = {
        1: {'id': 1, 'name': '电子产品', 'description': '各类电子设备'},
        2: {'id': 2, 'name': '服装鞋帽', 'description': '时尚服饰'},
        3: {'id': 3, 'name': '家居用品', 'description': '家庭生活用品'},
    }

    category = categories_data.get(category_id, {'id': category_id, 'name': '未知分类', 'description': ''})

    # 子分类数据
    subcategories_data = {
        1: [
            {'id': 11, 'name': '手机通讯'},
            {'id': 12, 'name': '电脑办公'},
            {'id': 13, 'name': '数码配件'},
        ],
        2: [
            {'id': 21, 'name': '男装'},
            {'id': 22, 'name': '女装'},
            {'id': 23, 'name': '鞋靴'},
        ],
        3: [
            {'id': 31, 'name': '家具'},
            {'id': 32, 'name': '家纺'},
            {'id': 33, 'name': '厨具'},
        ]
    }

    subcategories = subcategories_data.get(category_id, [])

    # 商品数据
    products_data = [
        {
            'id': 1,
            'name': '智能手表 Pro',
            'description': '健康监测、运动追踪、消息提醒',
            'price': 1299.00,
            'original_price': 1599.00,
            'rating': 4.5,
            'reviews_count': 128,
            'is_hot': True,
            'is_new': False,
            'image': 'https://picsum.photos/400/400?random=10'
        },
        {
            'id': 2,
            'name': '无线降噪耳机',
            'description': '主动降噪，高清音质，舒适佩戴',
            'price': 899.00,
            'original_price': 1099.00,
            'rating': 4.0,
            'reviews_count': 86,
            'is_hot': False,
            'is_new': True,
            'image': 'https://picsum.photos/400/400?random=11'
        }
    ]

    # 品牌数据
    brands = [
        {'id': 1, 'name': '苹果'},
        {'id': 2, 'name': '小米'},
        {'id': 3, 'name': '华为'},
    ]

    context = {
        'category': category,
        'subcategories': subcategories,
        'brands': brands,
        'products': products_data,
        'total_count': len(products_data),
    }
    return render(request, 'products/category.html', context)


def brand_list(request):
    """品牌列表"""
    brands = Brand.objects.filter(is_active=True)
    
    # 分页
    paginator = Paginator(brands, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
    }
    return render(request, 'products/brand_list.html', context)


@login_required
@csrf_exempt
def toggle_favorite(request):
    """切换收藏状态"""
    if request.method == 'POST':
        data = json.loads(request.body)
        product_id = data.get('product_id')
        
        try:
            product = Product.objects.get(id=product_id, is_active=True)
            favorite, created = Favorite.objects.get_or_create(
                user=request.user,
                product=product
            )
            
            if not created:
                # 已收藏，取消收藏
                favorite.delete()
                return JsonResponse({
                    'success': True, 
                    'favorited': False, 
                    'message': '取消收藏成功'
                })
            else:
                # 添加收藏
                return JsonResponse({
                    'success': True, 
                    'favorited': True, 
                    'message': '收藏成功'
                })
                
        except Product.DoesNotExist:
            return JsonResponse({'success': False, 'message': '商品不存在'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
def favorite_list(request):
    """收藏列表"""
    favorites = Favorite.objects.filter(user=request.user).select_related('product')
    
    # 分页
    paginator = Paginator(favorites, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
    }
    return render(request, 'products/favorite_list.html', context)


def search(request):
    """商品搜索"""
    query = request.GET.get('q', '')
    category_id = request.GET.get('category', '')
    brand_id = request.GET.get('brand', '')
    sort_by = request.GET.get('sort', 'default')
    is_hot = request.GET.get('is_hot', '')
    is_new = request.GET.get('is_new', '')
    min_price = request.GET.get('min_price', '')
    max_price = request.GET.get('max_price', '')

    # 使用静态数据模拟搜索结果
    all_products = [
        {
            'id': 1,
            'name': '智能手表 Pro',
            'description': '健康监测、运动追踪、消息提醒，功能强大的智能手表',
            'price': 1299.00,
            'original_price': 1599.00,
            'rating': 4.5,
            'reviews_count': 128,
            'is_hot': True,
            'is_new': False,
            'category_id': 11,  # 手机通讯
            'brand_id': 1,
            'image': 'https://picsum.photos/400/400?random=10'
        },
        {
            'id': 2,
            'name': '无线降噪耳机',
            'description': '主动降噪，高清音质，舒适佩戴，长达30小时续航',
            'price': 899.00,
            'original_price': 1099.00,
            'rating': 4.0,
            'reviews_count': 86,
            'is_hot': False,
            'is_new': True,
            'category_id': 13,  # 数码配件
            'brand_id': 2,
            'image': 'https://picsum.photos/400/400?random=11'
        },
        {
            'id': 3,
            'name': '智能音箱',
            'description': '语音助手，智能家居控制，高品质音响效果',
            'price': 399.00,
            'original_price': 499.00,
            'rating': 4.2,
            'reviews_count': 64,
            'is_hot': True,
            'is_new': False,
            'category_id': 13,  # 数码配件
            'brand_id': 1,
            'image': 'https://picsum.photos/400/400?random=12'
        },
        {
            'id': 4,
            'name': '运动背包',
            'description': '专业运动背包，大容量设计，防水耐用',
            'price': 299.00,
            'original_price': 399.00,
            'rating': 4.3,
            'reviews_count': 45,
            'is_hot': False,
            'is_new': False,
            'category_id': 21,  # 男装
            'brand_id': 3,
            'image': 'https://picsum.photos/400/400?random=13'
        },
        {
            'id': 5,
            'name': 'iPhone 15 Pro',
            'description': '最新款iPhone，A17 Pro芯片，钛金属设计',
            'price': 8999.00,
            'original_price': 9999.00,
            'rating': 4.8,
            'reviews_count': 256,
            'is_hot': True,
            'is_new': True,
            'category_id': 11,  # 手机通讯
            'brand_id': 1,
            'image': 'https://picsum.photos/400/400?random=14'
        },
        {
            'id': 6,
            'name': '小米14 Ultra',
            'description': '徕卡影像，骁龙8 Gen3，专业摄影手机',
            'price': 5999.00,
            'original_price': 6499.00,
            'rating': 4.6,
            'reviews_count': 189,
            'is_hot': True,
            'is_new': True,
            'category_id': 11,  # 手机通讯
            'brand_id': 2,
            'image': 'https://picsum.photos/400/400?random=15'
        },
        {
            'id': 7,
            'name': 'MacBook Pro 14',
            'description': 'M3 Pro芯片，14英寸Liquid Retina XDR显示屏',
            'price': 14999.00,
            'original_price': 15999.00,
            'rating': 4.7,
            'reviews_count': 98,
            'is_hot': False,
            'is_new': True,
            'category_id': 12,  # 电脑办公
            'brand_id': 1,
            'image': 'https://picsum.photos/400/400?random=16'
        },
        {
            'id': 8,
            'name': '机械键盘',
            'description': '青轴机械键盘，RGB背光，游戏办公两用',
            'price': 599.00,
            'original_price': 799.00,
            'rating': 4.4,
            'reviews_count': 156,
            'is_hot': False,
            'is_new': False,
            'category_id': 12,  # 电脑办公
            'brand_id': 3,
            'image': 'https://picsum.photos/400/400?random=17'
        }
    ]

    # 筛选逻辑
    filtered_products = all_products.copy()

    # 关键词搜索
    if query:
        filtered_products = [p for p in filtered_products if
                           query.lower() in p['name'].lower() or
                           query.lower() in p['description'].lower()]

    # 分类筛选
    if category_id:
        try:
            cat_id = int(category_id)
            filtered_products = [p for p in filtered_products if p['category_id'] == cat_id]
        except ValueError:
            pass

    # 品牌筛选
    if brand_id:
        try:
            b_id = int(brand_id)
            filtered_products = [p for p in filtered_products if p['brand_id'] == b_id]
        except ValueError:
            pass

    # 热销筛选
    if is_hot:
        filtered_products = [p for p in filtered_products if p['is_hot']]

    # 新品筛选
    if is_new:
        filtered_products = [p for p in filtered_products if p['is_new']]

    # 价格筛选
    if min_price:
        try:
            min_p = float(min_price)
            filtered_products = [p for p in filtered_products if p['price'] >= min_p]
        except ValueError:
            pass

    if max_price:
        try:
            max_p = float(max_price)
            filtered_products = [p for p in filtered_products if p['price'] <= max_p]
        except ValueError:
            pass

    # 排序
    if sort_by == 'price_asc':
        filtered_products.sort(key=lambda x: x['price'])
    elif sort_by == 'price_desc':
        filtered_products.sort(key=lambda x: x['price'], reverse=True)
    elif sort_by == 'rating':
        filtered_products.sort(key=lambda x: x['rating'], reverse=True)
    elif sort_by == 'new':
        filtered_products.sort(key=lambda x: x['is_new'], reverse=True)

    # 分类和品牌数据
    categories = [
        {'id': 11, 'name': '手机通讯'},
        {'id': 12, 'name': '电脑办公'},
        {'id': 13, 'name': '数码配件'},
        {'id': 21, 'name': '男装'},
        {'id': 22, 'name': '女装'},
        {'id': 23, 'name': '鞋靴'},
    ]

    brands = [
        {'id': 1, 'name': '苹果'},
        {'id': 2, 'name': '小米'},
        {'id': 3, 'name': '华为'},
        {'id': 4, 'name': '三星'},
    ]

    # 获取当前分类信息
    current_category = None
    if category_id:
        try:
            cat_id = int(category_id)
            current_category = next((c for c in categories if c['id'] == cat_id), None)
        except ValueError:
            pass

    context = {
        'query': query,
        'products': filtered_products,
        'categories': categories,
        'brands': brands,
        'current_category': current_category,
        'current_category_id': category_id,
        'current_brand': brand_id,
        'sort_by': sort_by,
        'is_hot': is_hot,
        'is_new': is_new,
        'min_price': min_price,
        'max_price': max_price,
        'total_count': len(filtered_products),
    }
    return render(request, 'products/search.html', context)


def search_suggestions(request):
    """搜索建议"""
    query = request.GET.get('q', '')
    suggestions = []

    if query and len(query) >= 2:
        # 搜索商品名称
        products = Product.objects.filter(
            name__icontains=query,
            is_active=True
        )[:10]

        suggestions = [{'name': product.name, 'id': product.id} for product in products]

    return JsonResponse({'suggestions': suggestions})

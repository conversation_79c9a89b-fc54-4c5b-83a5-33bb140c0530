{% extends 'admin_panel/base.html' %}

{% block title %}用户管理 - 智能电商管理后台{% endblock %}

{% block content %}
<div class="page-header">
    <h1><i class="fas fa-users me-2"></i>用户管理</h1>
</div>

<!-- 搜索和筛选 -->
<div class="card content-card mb-4">
    <div class="card-body">
        <form method="get" action="">
            <div class="row">
                <div class="col-md-3">
                    <input type="text" name="q" class="form-control" placeholder="搜索用户名或邮箱" value="{{ request.GET.q }}">
                </div>
                <div class="col-md-2">
                    <select name="status" class="form-select">
                        <option value="">所有状态</option>
                        <option value="1" {% if request.GET.status == "1" %}selected{% endif %}>活跃</option>
                        <option value="0" {% if request.GET.status == "0" %}selected{% endif %}>禁用</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="vip_level" class="form-select">
                        <option value="">所有VIP等级</option>
                        <option value="1" {% if request.GET.vip_level == "1" %}selected{% endif %}>青铜会员</option>
                        <option value="2" {% if request.GET.vip_level == "2" %}selected{% endif %}>白银会员</option>
                        <option value="3" {% if request.GET.vip_level == "3" %}selected{% endif %}>黄金会员</option>
                        <option value="4" {% if request.GET.vip_level == "4" %}selected{% endif %}>钻石会员</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>搜索
                    </button>
                </div>
                <div class="col-md-3">
                    <a href="?" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>清除筛选
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card content-card">
    <div class="card-header">
        <h5><i class="fas fa-list me-2"></i>用户列表</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>用户ID</th>
                        <th>用户信息</th>
                        <th>VIP等级</th>
                        <th>余额</th>
                        <th>消费总额</th>
                        <th>注册时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.id }}</td>
                        <td>
                            <div>
                                <strong>{{ user.username }}</strong>
                                <br>
                                <small class="text-muted">{{ user.email }}</small>
                                {% if user.userprofile.phone %}
                                <br>
                                <small class="text-muted">{{ user.userprofile.phone }}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if user.userprofile.vip_level %}
                                <span class="badge bg-warning">{{ user.userprofile.vip_level.name }}</span>
                            {% else %}
                                <span class="badge bg-secondary">普通用户</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="text-success">¥{{ user.userprofile.balance|default:"0.00" }}</span>
                        </td>
                        <td>
                            <span class="text-primary">¥{{ user.userprofile.total_consumption|default:"0.00" }}</span>
                        </td>
                        <td>{{ user.date_joined|date:"Y-m-d" }}</td>
                        <td>
                            {% if user.is_active %}
                                <span class="badge bg-success">活跃</span>
                            {% else %}
                                <span class="badge bg-danger">禁用</span>
                            {% endif %}
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-info me-1" title="查看详情" onclick="viewUser({{ user.id }})">
                                <i class="fas fa-eye"></i>
                            </button>
                            {% if user.is_active %}
                            <button class="btn btn-sm btn-outline-warning me-1" title="禁用用户" onclick="toggleUser({{ user.id }}, false)">
                                <i class="fas fa-ban"></i>
                            </button>
                            {% else %}
                            <button class="btn btn-sm btn-outline-success me-1" title="启用用户" onclick="toggleUser({{ user.id }}, true)">
                                <i class="fas fa-check"></i>
                            </button>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center text-muted">暂无用户数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <nav aria-label="用户分页">
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <span class="page-link">上一页</span>
                </li>
                <li class="page-item active">
                    <span class="page-link">1</span>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">2</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">3</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">下一页</a>
                </li>
            </ul>
        </nav>
    </div>
</div>

<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-user me-2"></i>用户详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr>
                                <td>用户名:</td>
                                <td id="detailUsername">-</td>
                            </tr>
                            <tr>
                                <td>邮箱:</td>
                                <td id="detailEmail">-</td>
                            </tr>
                            <tr>
                                <td>手机:</td>
                                <td id="detailPhone">-</td>
                            </tr>
                            <tr>
                                <td>性别:</td>
                                <td id="detailGender">-</td>
                            </tr>
                            <tr>
                                <td>生日:</td>
                                <td id="detailBirthday">-</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>会员信息</h6>
                        <table class="table table-sm">
                            <tr>
                                <td>VIP等级:</td>
                                <td id="detailVipLevel">-</td>
                            </tr>
                            <tr>
                                <td>账户余额:</td>
                                <td id="detailBalance">-</td>
                            </tr>
                            <tr>
                                <td>消费总额:</td>
                                <td id="detailConsumption">-</td>
                            </tr>
                            <tr>
                                <td>注册时间:</td>
                                <td id="detailJoinDate">-</td>
                            </tr>
                            <tr>
                                <td>最后登录:</td>
                                <td id="detailLastLogin">-</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <h6 class="mt-3">收货地址</h6>
                <div id="addressList">
                    <p class="text-muted">暂无收货地址</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewUser(userId) {
    // 这里应该发送AJAX请求获取用户详情
    alert('查看用户详情功能需要连接到实际的数据库');
    
    // 示例数据
    document.getElementById('detailUsername').textContent = 'testuser';
    document.getElementById('detailEmail').textContent = '<EMAIL>';
    document.getElementById('detailPhone').textContent = '13800138000';
    document.getElementById('detailGender').textContent = '男';
    document.getElementById('detailBirthday').textContent = '1990-01-01';
    document.getElementById('detailVipLevel').textContent = '黄金会员';
    document.getElementById('detailBalance').textContent = '¥1000.00';
    document.getElementById('detailConsumption').textContent = '¥8000.00';
    document.getElementById('detailJoinDate').textContent = '2024-01-01';
    document.getElementById('detailLastLogin').textContent = '2024-01-15 10:30';
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('userDetailModal'));
    modal.show();
}

function toggleUser(userId, enable) {
    const action = enable ? '启用' : '禁用';
    if (confirm(`确定要${action}这个用户吗？`)) {
        alert(`${action}用户功能需要连接到实际的数据库`);
    }
}
</script>
{% endblock %}

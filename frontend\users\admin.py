from django.contrib import admin
from .models import VIPLevel, UserProfile, Address, UserLoginLog, UserBalanceLog


@admin.register(VIPLevel)
class VIPLevelAdmin(admin.ModelAdmin):
    list_display = ['name', 'level', 'min_consumption', 'discount_rate', 'created_at']
    list_filter = ['level', 'created_at']
    search_fields = ['name']
    ordering = ['level']


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'phone', 'gender', 'vip_level', 'balance', 'total_consumption', 'points', 'created_at']
    list_filter = ['gender', 'vip_level', 'is_email_verified', 'is_phone_verified', 'created_at']
    search_fields = ['user__username', 'user__email', 'phone']
    readonly_fields = ['created_at', 'updated_at']
    raw_id_fields = ['user']


@admin.register(Address)
class AddressAdmin(admin.ModelAdmin):
    list_display = ['user', 'name', 'phone', 'province', 'city', 'district', 'is_default', 'created_at']
    list_filter = ['province', 'city', 'is_default', 'created_at']
    search_fields = ['user__username', 'name', 'phone', 'address']
    raw_id_fields = ['user']


@admin.register(UserLoginLog)
class UserLoginLogAdmin(admin.ModelAdmin):
    list_display = ['user', 'ip_address', 'login_time', 'is_success', 'failure_reason']
    list_filter = ['is_success', 'login_time']
    search_fields = ['user__username', 'ip_address']
    readonly_fields = ['login_time']
    raw_id_fields = ['user']


@admin.register(UserBalanceLog)
class UserBalanceLogAdmin(admin.ModelAdmin):
    list_display = ['user', 'operation_type', 'amount', 'balance_before', 'balance_after', 'created_at']
    list_filter = ['operation_type', 'created_at']
    search_fields = ['user__username', 'description', 'related_order']
    readonly_fields = ['created_at']
    raw_id_fields = ['user']

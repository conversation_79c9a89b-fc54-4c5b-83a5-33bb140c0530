from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from products.models import Product


class Cart(models.Model):
    """购物车"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '购物车'
        verbose_name_plural = '购物车'

    def __str__(self):
        return f"{self.user.username}的购物车"

    @property
    def total_items(self):
        """购物车商品总数量"""
        return self.items.aggregate(
            total=models.Sum('quantity')
        )['total'] or 0

    @property
    def total_amount(self):
        """购物车总金额"""
        total = 0
        for item in self.items.all():
            total += item.total_price
        return total

    @property
    def selected_items(self):
        """已选中的商品"""
        return self.items.filter(is_selected=True)

    @property
    def selected_total_amount(self):
        """已选中商品总金额"""
        total = 0
        for item in self.selected_items:
            total += item.total_price
        return total

    def clear(self):
        """清空购物车"""
        self.items.all().delete()

    def clear_selected(self):
        """清空已选中的商品"""
        self.items.filter(is_selected=True).delete()


class CartItem(models.Model):
    """购物车商品项"""
    cart = models.ForeignKey(Cart, on_delete=models.CASCADE, related_name='items', verbose_name='购物车')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name='商品')
    quantity = models.IntegerField(
        default=1, 
        validators=[MinValueValidator(1)], 
        verbose_name='数量'
    )
    is_selected = models.BooleanField(default=True, verbose_name='是否选中')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='添加时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '购物车商品项'
        verbose_name_plural = '购物车商品项'
        unique_together = ['cart', 'product']
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.cart.user.username} - {self.product.name} x {self.quantity}"

    @property
    def total_price(self):
        """商品小计"""
        return self.product.price * self.quantity

    @property
    def is_available(self):
        """商品是否可用（有库存且上架）"""
        return self.product.is_active and self.product.stock >= self.quantity

    @property
    def stock_status(self):
        """库存状态"""
        if not self.product.is_active:
            return 'inactive'
        elif self.product.stock <= 0:
            return 'out_of_stock'
        elif self.product.stock < self.quantity:
            return 'insufficient'
        else:
            return 'available'

    def update_quantity(self, quantity):
        """更新数量"""
        if quantity <= 0:
            self.delete()
            return False
        
        if quantity > self.product.stock:
            return False
        
        self.quantity = quantity
        self.save()
        return True

    def increase_quantity(self, amount=1):
        """增加数量"""
        new_quantity = self.quantity + amount
        return self.update_quantity(new_quantity)

    def decrease_quantity(self, amount=1):
        """减少数量"""
        new_quantity = self.quantity - amount
        return self.update_quantity(new_quantity)


class CartManager:
    """购物车管理器"""
    
    @staticmethod
    def get_or_create_cart(user):
        """获取或创建购物车"""
        cart, created = Cart.objects.get_or_create(user=user)
        return cart

    @staticmethod
    def add_product(user, product, quantity=1):
        """添加商品到购物车"""
        if not product.is_active:
            return False, "商品已下架"
        
        if product.stock < quantity:
            return False, "库存不足"
        
        cart = CartManager.get_or_create_cart(user)
        
        try:
            cart_item = CartItem.objects.get(cart=cart, product=product)
            # 商品已存在，更新数量
            new_quantity = cart_item.quantity + quantity
            if new_quantity > product.stock:
                return False, "库存不足"
            cart_item.quantity = new_quantity
            cart_item.save()
        except CartItem.DoesNotExist:
            # 新商品，创建购物车项
            CartItem.objects.create(
                cart=cart,
                product=product,
                quantity=quantity
            )
        
        return True, "添加成功"

    @staticmethod
    def remove_product(user, product):
        """从购物车移除商品"""
        try:
            cart = Cart.objects.get(user=user)
            cart_item = CartItem.objects.get(cart=cart, product=product)
            cart_item.delete()
            return True, "移除成功"
        except (Cart.DoesNotExist, CartItem.DoesNotExist):
            return False, "商品不在购物车中"

    @staticmethod
    def update_quantity(user, product, quantity):
        """更新商品数量"""
        try:
            cart = Cart.objects.get(user=user)
            cart_item = CartItem.objects.get(cart=cart, product=product)
            
            if quantity <= 0:
                cart_item.delete()
                return True, "商品已移除"
            
            if quantity > product.stock:
                return False, "库存不足"
            
            cart_item.quantity = quantity
            cart_item.save()
            return True, "数量更新成功"
        except (Cart.DoesNotExist, CartItem.DoesNotExist):
            return False, "商品不在购物车中"

    @staticmethod
    def toggle_selection(user, product):
        """切换商品选中状态"""
        try:
            cart = Cart.objects.get(user=user)
            cart_item = CartItem.objects.get(cart=cart, product=product)
            cart_item.is_selected = not cart_item.is_selected
            cart_item.save()
            return True, "选中状态已更新"
        except (Cart.DoesNotExist, CartItem.DoesNotExist):
            return False, "商品不在购物车中"

    @staticmethod
    def select_all(user, selected=True):
        """全选/取消全选"""
        try:
            cart = Cart.objects.get(user=user)
            cart.items.update(is_selected=selected)
            return True, "全选状态已更新"
        except Cart.DoesNotExist:
            return False, "购物车不存在"

    @staticmethod
    def get_cart_summary(user):
        """获取购物车摘要"""
        try:
            cart = Cart.objects.get(user=user)
            return {
                'total_items': cart.total_items,
                'total_amount': cart.total_amount,
                'selected_items': cart.selected_items.count(),
                'selected_amount': cart.selected_total_amount,
            }
        except Cart.DoesNotExist:
            return {
                'total_items': 0,
                'total_amount': 0,
                'selected_items': 0,
                'selected_amount': 0,
            }

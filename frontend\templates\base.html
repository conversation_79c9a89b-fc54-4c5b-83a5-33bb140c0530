<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}智能电商{% endblock %}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'orange-primary': '#FF6B35',
                        'orange-secondary': '#FF8C42',
                        'orange-accent': '#FFA500',
                        'orange-light': '#FFE5D9',
                        'orange-dark': '#E55A2B'
                    }
                }
            }
        }
    </script>

    <style>
        body {
            font-family: 'Microsoft YaHei', 'Inter', sans-serif;
        }

        .group:hover .group-hover\\:scale-105 {
            transform: scale(1.05);
        }

        html {
            scroll-behavior: smooth;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .animate-fadeIn {
            animation: fadeIn 0.5s ease-in-out;
        }

        .transition-colors {
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .cart-badge {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="font-sans bg-gray-50">
    <!-- 顶部导航 -->
    <header class="sticky top-0 z-50 bg-white shadow-sm">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between py-3">
                <div class="flex items-center">
                    <a href="{% url 'products:index' %}" class="text-2xl font-bold text-orange-primary">
                        <i class="fa fa-shopping-bag mr-2"></i>智能电商
                    </a>
                </div>

                <div class="hidden md:flex items-center space-x-4">
                    <a href="{% url 'products:index' %}" class="text-gray-700 hover:text-orange-primary transition-colors">首页</a>
                    <a href="{% url 'products:search' %}" class="text-gray-700 hover:text-orange-primary transition-colors">商品</a>
                    <a href="{% url 'products:category_list' %}" class="text-gray-700 hover:text-orange-primary transition-colors">分类</a>
                    <a href="{% url 'products:search' %}?is_hot=1" class="text-gray-700 hover:text-orange-primary transition-colors">促销</a>
                    <a href="{% url 'products:search' %}?is_new=1" class="text-gray-700 hover:text-orange-primary transition-colors">新品</a>
                    <a href="http://127.0.0.1:8003/" target="_blank" class="text-gray-700 hover:text-orange-primary transition-colors">
                        <i class="fa fa-cog mr-1"></i>商家后台
                    </a>
                </div>

                <div class="flex items-center space-x-4">
                    <div class="relative hidden md:block">
                        <form action="{% url 'products:search' %}" method="get" class="relative">
                            <input type="text" name="q" placeholder="搜索商品..." class="w-64 py-2 px-4 rounded-full border focus:outline-none focus:ring-2 focus:ring-orange-primary/30" value="{{ request.GET.q }}">
                            <button type="submit" class="absolute right-3 top-2 text-gray-400 hover:text-orange-primary">
                                <i class="fa fa-search"></i>
                            </button>
                        </form>
                    </div>

                    {% if user.is_authenticated %}
                        <div class="relative">
                            <a href="{% url 'users:profile' %}" class="text-gray-700 hover:text-orange-primary transition-colors">
                                <i class="fa fa-user-circle-o text-xl"></i>
                            </a>
                        </div>

                        <div class="relative">
                            <a href="{% url 'cart:view' %}" class="text-gray-700 hover:text-orange-primary transition-colors relative">
                                <i class="fa fa-shopping-cart text-xl"></i>
                                <span class="absolute -top-2 -right-2 bg-orange-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center cart-badge">0</span>
                            </a>
                        </div>

                        <div class="relative group">
                            <button class="text-gray-700 hover:text-orange-primary transition-colors flex items-center">
                                <span class="mr-1">{{ user.username }}</span>
                                <i class="fa fa-angle-down"></i>
                            </button>
                            <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 hidden group-hover:block z-50">
                                <a href="{% url 'users:profile' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fa fa-user mr-2"></i>个人中心
                                </a>
                                <a href="{% url 'orders:list' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fa fa-list-alt mr-2"></i>我的订单
                                </a>
                                <a href="{% url 'cart:view' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fa fa-shopping-cart mr-2"></i>购物车
                                </a>
                                <div class="border-t border-gray-100"></div>
                                <a href="{% url 'users:logout' %}" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100" onclick="return confirm('确定要退出登录吗？')">
                                    <i class="fa fa-sign-out mr-2"></i>退出登录
                                </a>
                            </div>
                        </div>
                    {% else %}
                        <a href="{% url 'users:login' %}" class="text-gray-700 hover:text-orange-primary transition-colors">
                            <i class="fa fa-user-circle-o text-xl"></i>
                        </a>
                        <a href="{% url 'users:register' %}" class="bg-orange-primary hover:bg-orange-secondary text-white px-4 py-2 rounded-full transition-colors">
                            注册
                        </a>
                    {% endif %}

                    <button class="md:hidden text-gray-700 hover:text-orange-primary transition-colors" id="mobile-menu-button">
                        <i class="fa fa-bars text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- 移动端菜单 -->
            <div class="md:hidden hidden" id="mobile-menu">
                <div class="py-2 space-y-1">
                    <a href="{% url 'products:index' %}" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">首页</a>
                    <a href="{% url 'products:search' %}" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">商品</a>
                    <a href="{% url 'products:category_list' %}" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">分类</a>
                    <a href="{% url 'products:search' %}?is_hot=1" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">促销</a>
                    <a href="{% url 'products:search' %}?is_new=1" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">新品</a>
                    <a href="http://127.0.0.1:8003/" target="_blank" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">
                        <i class="fa fa-cog mr-2"></i>商家后台
                    </a>
                    {% if user.is_authenticated %}
                        <a href="{% url 'orders:list' %}" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">
                            <i class="fa fa-list-alt mr-2"></i>我的订单
                        </a>
                        <a href="{% url 'cart:view' %}" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">
                            <i class="fa fa-shopping-cart mr-2"></i>购物车
                        </a>
                    {% endif %}
                    <div class="relative mt-3">
                        <form action="{% url 'products:search' %}" method="get" class="relative">
                            <input type="text" name="q" placeholder="搜索商品..." class="w-full py-2 px-4 rounded-full border focus:outline-none focus:ring-2 focus:ring-orange-primary/30" value="{{ request.GET.q }}">
                            <button type="submit" class="absolute right-3 top-2 text-gray-400 hover:text-orange-primary">
                                <i class="fa fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 消息提示 -->
    {% if messages %}
        <div class="container mx-auto px-4 mt-4">
            {% for message in messages %}
                <div class="bg-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% elif message.tags == 'warning' %}yellow{% else %}blue{% endif %}-100 border border-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% elif message.tags == 'warning' %}yellow{% else %}blue{% endif %}-400 text-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% elif message.tags == 'warning' %}yellow{% else %}blue{% endif %}-700 px-4 py-3 rounded mb-4">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- 主要内容 -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-12 mt-16">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold text-orange-primary mb-4">智能电商</h3>
                    <p class="text-gray-300 mb-4">专业的在线购物平台，为您提供优质的商品和服务。</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-orange-primary transition-colors">
                            <i class="fa fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-orange-primary transition-colors">
                            <i class="fa fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-orange-primary transition-colors">
                            <i class="fa fa-instagram text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-orange-primary transition-colors">
                            <i class="fa fa-weibo text-xl"></i>
                        </a>
                    </div>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">快速链接</h4>
                    <ul class="space-y-2">
                        <li><a href="{% url 'products:index' %}" class="text-gray-300 hover:text-orange-primary transition-colors">首页</a></li>
                        <li><a href="{% url 'products:search' %}" class="text-gray-300 hover:text-orange-primary transition-colors">商品</a></li>
                        <li><a href="{% url 'products:category_list' %}" class="text-gray-300 hover:text-orange-primary transition-colors">分类</a></li>
                        <li><a href="{% url 'products:search' %}?is_hot=1" class="text-gray-300 hover:text-orange-primary transition-colors">促销活动</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">客户服务</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-orange-primary transition-colors">帮助中心</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-orange-primary transition-colors">退换货政策</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-orange-primary transition-colors">配送说明</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-orange-primary transition-colors">联系我们</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">联系我们</h4>
                    <div class="space-y-2 text-gray-300">
                        <p><i class="fa fa-envelope mr-2"></i> <EMAIL></p>
                        <p><i class="fa fa-phone mr-2"></i> ************</p>
                        <p><i class="fa fa-map-marker mr-2"></i> 北京市朝阳区xxx街道</p>
                        <p><i class="fa fa-clock-o mr-2"></i> 9:00-18:00 (工作日)</p>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 智能电商. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 移动端菜单切换
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // 购物车数量更新
        function updateCartCount() {
            // 这里可以通过AJAX获取购物车数量
            // 暂时使用静态数据
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            updateCartCount();
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>

# 🚀 智能电商系统安装指南

## 📋 系统要求

### 软件环境
- **Python**: 3.8 或更高版本
- **MySQL**: 5.7 或更高版本
- **操作系统**: Windows 10/11, macOS, Linux

### 硬件要求
- **内存**: 最少 4GB RAM
- **存储**: 最少 2GB 可用空间
- **网络**: 稳定的互联网连接（用于下载依赖包）

## 🛠️ 安装步骤

### 1. 准备 Python 环境

#### Windows 用户
1. 从 [Python官网](https://www.python.org/downloads/) 下载 Python 3.8+
2. 安装时勾选 "Add Python to PATH"
3. 验证安装：
```cmd
python --version
pip --version
```

#### macOS 用户
```bash
# 使用 Homebrew 安装
brew install python3
```

#### Linux 用户
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip

# CentOS/RHEL
sudo yum install python3 python3-pip
```

### 2. 安装 MySQL

#### Windows 用户
1. 从 [MySQL官网](https://dev.mysql.com/downloads/mysql/) 下载 MySQL Installer
2. 安装 MySQL Server 和 MySQL Workbench
3. 设置 root 用户密码为 `123456`

#### macOS 用户
```bash
# 使用 Homebrew 安装
brew install mysql
brew services start mysql

# 设置密码
mysql_secure_installation
```

#### Linux 用户
```bash
# Ubuntu/Debian
sudo apt install mysql-server
sudo mysql_secure_installation

# CentOS/RHEL
sudo yum install mysql-server
sudo systemctl start mysqld
sudo mysql_secure_installation
```

### 3. 创建数据库

连接到 MySQL 并创建数据库：

```sql
mysql -u root -p
CREATE DATABASE shopping CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;
```

### 4. 下载项目

```bash
# 如果使用 Git
git clone <repository-url>
cd shoping

# 或者下载 ZIP 文件并解压
```

### 5. 安装 Python 依赖

```bash
# 建议创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 6. 配置数据库连接

确认数据库配置正确（已在代码中配置）：
- 数据库名: `shopping`
- 用户名: `root`
- 密码: `123456`
- 主机: `localhost`
- 端口: `3306`

### 7. 初始化数据库

#### Windows 用户
```cmd
# 运行初始化脚本
init_database.bat
```

#### macOS/Linux 用户
```bash
# 前台数据库迁移
cd frontend
python manage.py makemigrations shop
python manage.py migrate

# 后台数据库迁移
cd ../backend
python manage.py makemigrations management
python manage.py migrate

cd ..
```

### 8. 启动服务

#### Windows 用户
```cmd
# 运行启动脚本
start_servers.bat
```

#### macOS/Linux 用户
```bash
# 启动前台服务（新终端窗口）
cd frontend
python manage.py runserver 127.0.0.1:8001

# 启动后台服务（新终端窗口）
cd backend
python manage.py runserver 127.0.0.1:8003
```

## 🌐 访问系统

### 前台商城
- **地址**: http://127.0.0.1:8001/
- **功能**: 用户购物、商品浏览、订单管理

### 后台管理
- **地址**: http://127.0.0.1:8003/dashboard/
- **功能**: 商品管理、订单管理、用户管理

## 👤 创建管理员账号

1. 访问后台注册页面：http://127.0.0.1:8003/register/
2. 填写管理员信息：
   - 用户名：admin
   - 邮箱：<EMAIL>
   - 密码：admin123
3. 注册成功后即可登录后台管理系统

## 🧪 测试系统

### 1. 测试前台功能
1. 访问前台首页
2. 注册普通用户账号
3. 浏览商品（需要先在后台添加商品）
4. 测试购物车和订单功能

### 2. 测试后台功能
1. 登录后台管理系统
2. 添加商品分类
3. 添加商品
4. 查看订单和用户信息

## 🔧 常见问题

### Q1: 数据库连接失败
**解决方案**:
1. 确认 MySQL 服务已启动
2. 检查数据库用户名和密码
3. 确认数据库 `shopping` 已创建

### Q2: 端口被占用
**解决方案**:
```bash
# 查看端口占用
netstat -ano | findstr :8001
netstat -ano | findstr :8003

# 修改端口（在 manage.py runserver 命令中指定其他端口）
python manage.py runserver 127.0.0.1:8002
```

### Q3: 静态文件无法加载
**解决方案**:
1. 确认 DEBUG = True
2. 检查 STATIC_URL 和 STATICFILES_DIRS 配置
3. 运行 `python manage.py collectstatic`

### Q4: 图片上传失败
**解决方案**:
1. 确认 media 目录存在且有写权限
2. 检查 MEDIA_URL 和 MEDIA_ROOT 配置
3. 确认 Pillow 库已正确安装

### Q5: 依赖包安装失败
**解决方案**:
```bash
# 升级 pip
pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 单独安装问题包
pip install mysqlclient
```

## 📊 性能优化

### 1. 数据库优化
```sql
-- 为常用字段添加索引
ALTER TABLE shop_product ADD INDEX idx_category (category_id);
ALTER TABLE shop_product ADD INDEX idx_active (is_active);
ALTER TABLE shop_order ADD INDEX idx_user (user_id);
ALTER TABLE shop_order ADD INDEX idx_status (status);
```

### 2. 缓存配置
在 settings.py 中添加缓存配置：
```python
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
        'LOCATION': 'cache_table',
    }
}
```

### 3. 静态文件优化
```python
# 生产环境配置
STATIC_ROOT = '/path/to/static/'
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'
```

## 🚀 部署到生产环境

### 1. 环境配置
```python
# settings.py 生产环境配置
DEBUG = False
ALLOWED_HOSTS = ['your-domain.com', 'www.your-domain.com']

# 安全设置
SECURE_SSL_REDIRECT = True
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
```

### 2. Web 服务器配置
推荐使用 Nginx + Gunicorn：

```bash
# 安装 Gunicorn
pip install gunicorn

# 启动前台服务
cd frontend
gunicorn frontend.wsgi:application --bind 0.0.0.0:8001

# 启动后台服务
cd backend
gunicorn backend.wsgi:application --bind 0.0.0.0:8003
```

## 📞 技术支持

如果在安装过程中遇到问题，请：

1. 查看错误日志
2. 检查系统要求是否满足
3. 参考常见问题解决方案
4. 联系技术支持：<EMAIL>

---

🎉 **恭喜！智能电商系统安装完成！**

现在您可以开始使用这个功能完整的电商平台了。

{% extends 'shop/base.html' %}

{% block title %}智能电商 - 首页{% endblock %}

{% block content %}
<!-- 轮播图 -->
{% if banners %}
<div id="carouselBanner" class="carousel slide" data-bs-ride="carousel">
    <div class="carousel-indicators">
        {% for banner in banners %}
        <button type="button" data-bs-target="#carouselBanner" data-bs-slide-to="{{ forloop.counter0 }}" 
                {% if forloop.first %}class="active"{% endif %}></button>
        {% endfor %}
    </div>
    <div class="carousel-inner">
        {% for banner in banners %}
        <div class="carousel-item {% if forloop.first %}active{% endif %}">
            <img src="{{ banner.image.url }}" class="d-block w-100" alt="{{ banner.title }}">
            <div class="carousel-caption d-none d-md-block">
                <h5>{{ banner.title }}</h5>
            </div>
        </div>
        {% endfor %}
    </div>
    <button class="carousel-control-prev" type="button" data-bs-target="#carouselBanner" data-bs-slide="prev">
        <span class="carousel-control-prev-icon"></span>
    </button>
    <button class="carousel-control-next" type="button" data-bs-target="#carouselBanner" data-bs-slide="next">
        <span class="carousel-control-next-icon"></span>
    </button>
</div>
{% endif %}

<div class="container my-5">
    <!-- 商品分类 -->
    {% if main_categories %}
    <section class="mb-5">
        <h2 class="text-center mb-4">商品分类</h2>
        <div class="row">
            {% for category in main_categories %}
            <div class="col-6 col-md-4 col-lg-2 mb-3">
                <a href="{% url 'shop:product_list' %}?category={{ category.id }}" class="text-decoration-none">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            {% if category.icon %}
                                <img src="{{ category.icon.url }}" alt="{{ category.name }}" class="category-icon mb-2" style="width: 50px; height: 50px;">
                            {% else %}
                                <i class="fas fa-cube category-icon mb-2"></i>
                            {% endif %}
                            <h6 class="card-title">{{ category.name }}</h6>
                        </div>
                    </div>
                </a>
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}

    <!-- 热门商品 -->
    {% if hot_products %}
    <section class="mb-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>热门商品</h2>
            <a href="{% url 'shop:product_list' %}" class="btn btn-outline-primary">查看更多</a>
        </div>
        <div class="row">
            {% for product in hot_products %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card product-card h-100">
                    <img src="{{ product.main_image.url }}" class="card-img-top" alt="{{ product.name }}">
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ product.name }}</h5>
                        <p class="card-text text-muted small">{{ product.description|truncatechars:50 }}</p>
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="h5 text-primary">¥{{ product.price }}</span>
                                    {% if product.original_price %}
                                    <small class="text-muted text-decoration-line-through">¥{{ product.original_price }}</small>
                                    {% endif %}
                                </div>
                                <small class="text-muted">销量: {{ product.sales }}</small>
                            </div>
                            <div class="d-flex justify-content-between mt-2">
                                <a href="{% url 'shop:product_detail' product.id %}" class="btn btn-outline-primary btn-sm">查看详情</a>
                                {% if user.is_authenticated %}
                                <button class="btn btn-primary btn-sm add-to-cart" data-product-id="{{ product.id }}">
                                    <i class="fas fa-cart-plus"></i> 加入购物车
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}

    <!-- 新品上市 -->
    {% if new_products %}
    <section class="mb-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>新品上市</h2>
            <a href="{% url 'shop:product_list' %}" class="btn btn-outline-primary">查看更多</a>
        </div>
        <div class="row">
            {% for product in new_products %}
            <div class="col-md-6 col-lg-3 mb-4">
                <div class="card product-card h-100">
                    <div class="position-relative">
                        <img src="{{ product.main_image.url }}" class="card-img-top" alt="{{ product.name }}">
                        <span class="badge bg-success position-absolute top-0 end-0 m-2">新品</span>
                    </div>
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">{{ product.name }}</h6>
                        <p class="card-text text-muted small">{{ product.description|truncatechars:30 }}</p>
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="h6 text-primary">¥{{ product.price }}</span>
                                <small class="text-muted">库存: {{ product.stock }}</small>
                            </div>
                            <div class="d-flex justify-content-between mt-2">
                                <a href="{% url 'shop:product_detail' product.id %}" class="btn btn-outline-primary btn-sm">查看</a>
                                {% if user.is_authenticated %}
                                <button class="btn btn-primary btn-sm add-to-cart" data-product-id="{{ product.id }}">
                                    <i class="fas fa-cart-plus"></i>
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 添加到购物车
    $('.add-to-cart').click(function() {
        var productId = $(this).data('product-id');
        var button = $(this);
        
        $.ajax({
            url: '{% url "shop:add_to_cart" %}',
            type: 'POST',
            data: JSON.stringify({
                'product_id': productId,
                'quantity': 1
            }),
            contentType: 'application/json',
            headers: {
                'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.success) {
                    button.html('<i class="fas fa-check"></i> 已添加');
                    button.removeClass('btn-primary').addClass('btn-success');
                    setTimeout(function() {
                        button.html('<i class="fas fa-cart-plus"></i> 加入购物车');
                        button.removeClass('btn-success').addClass('btn-primary');
                    }, 2000);
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('添加失败，请重试');
            }
        });
    });
});
</script>
{% endblock %}

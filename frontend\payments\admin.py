from django.contrib import admin
from .models import Payment, PaymentLog


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['payment_no', 'order', 'user', 'payment_method', 'amount', 'status', 'created_at']
    list_filter = ['payment_method', 'status', 'created_at']
    search_fields = ['payment_no', 'order__order_no', 'user__username']
    readonly_fields = ['payment_no', 'created_at', 'paid_at']


@admin.register(PaymentLog)
class PaymentLogAdmin(admin.ModelAdmin):
    list_display = ['payment', 'action', 'created_at']
    list_filter = ['action', 'created_at']
    search_fields = ['payment__payment_no', 'action']

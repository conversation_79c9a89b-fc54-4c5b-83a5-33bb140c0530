{% extends 'admin_panel/base.html' %}

{% block title %}商品管理 - 智能电商管理后台{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-box me-2"></i>商品管理</h1>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
            <i class="fas fa-plus me-2"></i>添加商品
        </button>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card content-card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <input type="text" class="form-control" placeholder="搜索商品名称或SKU">
            </div>
            <div class="col-md-2">
                <select class="form-select">
                    <option value="">所有分类</option>
                    <option value="1">电子产品</option>
                    <option value="2">服装鞋帽</option>
                    <option value="3">家居用品</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select">
                    <option value="">所有状态</option>
                    <option value="1">上架</option>
                    <option value="0">下架</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-primary">
                    <i class="fas fa-search me-2"></i>搜索
                </button>
            </div>
        </div>
    </div>
</div>

<div class="card content-card">
    <div class="card-header">
        <h5><i class="fas fa-list me-2"></i>商品列表</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>商品图片</th>
                        <th>商品信息</th>
                        <th>分类</th>
                        <th>价格</th>
                        <th>库存</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <img src="https://via.placeholder.com/60x60" class="img-thumbnail" alt="商品图片">
                        </td>
                        <td>
                            <div>
                                <strong>iPhone 15 Pro Max</strong>
                                <br>
                                <small class="text-muted">SKU: IP15PM001</small>
                                <br>
                                <span class="badge bg-warning">热门</span>
                                <span class="badge bg-info">新品</span>
                            </div>
                        </td>
                        <td>手机通讯</td>
                        <td>
                            <div>
                                <span class="text-danger">¥9999.00</span>
                                <br>
                                <small class="text-muted text-decoration-line-through">¥10999.00</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-success">50</span>
                        </td>
                        <td>
                            <span class="badge bg-success">上架</span>
                        </td>
                        <td>2024-01-01</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning me-1" title="下架">
                                <i class="fas fa-eye-slash"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <img src="https://via.placeholder.com/60x60" class="img-thumbnail" alt="商品图片">
                        </td>
                        <td>
                            <div>
                                <strong>小米14 Ultra</strong>
                                <br>
                                <small class="text-muted">SKU: MI14U001</small>
                                <br>
                                <span class="badge bg-warning">热门</span>
                            </div>
                        </td>
                        <td>手机通讯</td>
                        <td>
                            <div>
                                <span class="text-danger">¥5999.00</span>
                                <br>
                                <small class="text-muted text-decoration-line-through">¥6499.00</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-warning">30</span>
                        </td>
                        <td>
                            <span class="badge bg-success">上架</span>
                        </td>
                        <td>2024-01-01</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning me-1" title="下架">
                                <i class="fas fa-eye-slash"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <img src="https://via.placeholder.com/60x60" class="img-thumbnail" alt="商品图片">
                        </td>
                        <td>
                            <div>
                                <strong>MacBook Pro 16英寸</strong>
                                <br>
                                <small class="text-muted">SKU: MBP16001</small>
                                <br>
                                <span class="badge bg-info">新品</span>
                            </div>
                        </td>
                        <td>电脑办公</td>
                        <td>
                            <div>
                                <span class="text-danger">¥25999.00</span>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-danger">20</span>
                        </td>
                        <td>
                            <span class="badge bg-success">上架</span>
                        </td>
                        <td>2024-01-01</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning me-1" title="下架">
                                <i class="fas fa-eye-slash"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <nav aria-label="商品分页">
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <span class="page-link">上一页</span>
                </li>
                <li class="page-item active">
                    <span class="page-link">1</span>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">2</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">3</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">下一页</a>
                </li>
            </ul>
        </nav>
    </div>
</div>

<!-- 添加商品模态框 -->
<div class="modal fade" id="addProductModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>添加商品</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addProductForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="productName" class="form-label">商品名称</label>
                                <input type="text" class="form-control" id="productName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="productSku" class="form-label">商品SKU</label>
                                <input type="text" class="form-control" id="productSku" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="productCategory" class="form-label">商品分类</label>
                                <select class="form-select" id="productCategory" required>
                                    <option value="">选择分类</option>
                                    <option value="1">手机通讯</option>
                                    <option value="2">电脑办公</option>
                                    <option value="3">数码配件</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="productBrand" class="form-label">商品品牌</label>
                                <select class="form-select" id="productBrand">
                                    <option value="">选择品牌</option>
                                    <option value="1">苹果</option>
                                    <option value="2">小米</option>
                                    <option value="3">华为</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="productPrice" class="form-label">销售价格</label>
                                <input type="number" class="form-control" id="productPrice" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="productOriginalPrice" class="form-label">原价</label>
                                <input type="number" class="form-control" id="productOriginalPrice" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="productStock" class="form-label">库存数量</label>
                                <input type="number" class="form-control" id="productStock" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="productDescription" class="form-label">商品描述</label>
                        <textarea class="form-control" id="productDescription" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isHot">
                                <label class="form-check-label" for="isHot">热门商品</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isNew">
                                <label class="form-check-label" for="isNew">新品上市</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isActive" checked>
                                <label class="form-check-label" for="isActive">立即上架</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addProduct()">
                    <i class="fas fa-save me-2"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function addProduct() {
    // 获取表单数据
    const name = document.getElementById('productName').value;
    const sku = document.getElementById('productSku').value;
    const category = document.getElementById('productCategory').value;
    const price = document.getElementById('productPrice').value;
    const stock = document.getElementById('productStock').value;
    
    if (!name.trim() || !sku.trim() || !category || !price || !stock) {
        alert('请填写必填字段');
        return;
    }
    
    // 这里应该发送AJAX请求到后端
    alert('商品添加功能需要连接到实际的数据库');
    
    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
    modal.hide();
    
    // 重置表单
    document.getElementById('addProductForm').reset();
}
</script>
{% endblock %}

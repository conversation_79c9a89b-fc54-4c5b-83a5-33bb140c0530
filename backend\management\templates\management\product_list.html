{% extends 'management/base.html' %}

{% block title %}商品管理 - 智能电商管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-box me-2"></i>商品管理</h1>
    <a href="{% url 'management:product_add' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>添加商品
    </a>
</div>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <input type="text" class="form-control" name="search" placeholder="搜索商品名称..." value="{{ search }}">
            </div>
            <div class="col-md-3">
                <select class="form-select" name="category">
                    <option value="">全部分类</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}" {% if current_category == category.id|stringformat:'s' %}selected{% endif %}>
                        {{ category.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="status">
                    <option value="">全部状态</option>
                    <option value="active" {% if current_status == 'active' %}selected{% endif %}>已上架</option>
                    <option value="inactive" {% if current_status == 'inactive' %}selected{% endif %}>已下架</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-search me-2"></i>搜索
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 商品列表 -->
<div class="card">
    <div class="card-header">
        <h5>商品列表 (共 {{ page_obj.paginator.count }} 件)</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>商品图片</th>
                        <th>商品信息</th>
                        <th>分类</th>
                        <th>价格</th>
                        <th>库存</th>
                        <th>销量</th>
                        <th>状态</th>
                        <th>标签</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in page_obj %}
                    <tr>
                        <td>
                            <img src="{{ product.main_image.url }}" alt="{{ product.name }}" 
                                 class="rounded" style="width: 60px; height: 60px; object-fit: cover;">
                        </td>
                        <td>
                            <div>
                                <h6 class="mb-1">{{ product.name }}</h6>
                                <small class="text-muted">{{ product.description|truncatechars:50 }}</small>
                                <br>
                                <small class="text-muted">创建时间: {{ product.created_at|date:"Y-m-d H:i" }}</small>
                            </div>
                        </td>
                        <td>{{ product.category.name }}</td>
                        <td>
                            <span class="text-primary fw-bold">¥{{ product.price }}</span>
                            {% if product.original_price %}
                            <br>
                            <small class="text-muted text-decoration-line-through">¥{{ product.original_price }}</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.stock > 10 %}
                            <span class="text-success">{{ product.stock }}</span>
                            {% elif product.stock > 0 %}
                            <span class="text-warning">{{ product.stock }}</span>
                            {% else %}
                            <span class="text-danger">{{ product.stock }}</span>
                            {% endif %}
                        </td>
                        <td>{{ product.sales }}</td>
                        <td>
                            {% if product.is_active %}
                            <span class="badge bg-success">已上架</span>
                            {% else %}
                            <span class="badge bg-secondary">已下架</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.is_hot %}
                            <span class="badge bg-danger me-1">热门</span>
                            {% endif %}
                            {% if product.is_new %}
                            <span class="badge bg-success">新品</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'management:product_edit' product.id %}" class="btn btn-outline-primary" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-outline-danger" onclick="deleteProduct({{ product.id }})" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <a href="http://127.0.0.1:8001/product/{{ product.id }}/" target="_blank" class="btn btn-outline-info" title="预览">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="text-center text-muted py-4">
                            <i class="fas fa-box-open fa-2x mb-2"></i>
                            <br>暂无商品
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分页 -->
{% if page_obj.has_other_pages %}
<nav aria-label="商品分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">
                上一页
            </a>
        </li>
        {% endif %}
        
        {% for num in page_obj.paginator.page_range %}
        {% if page_obj.number == num %}
        <li class="page-item active">
            <span class="page-link">{{ num }}</span>
        </li>
        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
        <li class="page-item">
            <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">
                {{ num }}
            </a>
        </li>
        {% endif %}
        {% endfor %}
        
        {% if page_obj.has_next %}
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">
                下一页
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function deleteProduct(productId) {
    if (confirm('确定要删除这个商品吗？删除后无法恢复！')) {
        window.location.href = '/products/' + productId + '/delete/';
    }
}

$(document).ready(function() {
    // 批量操作功能可以在这里添加
});
</script>
{% endblock %}

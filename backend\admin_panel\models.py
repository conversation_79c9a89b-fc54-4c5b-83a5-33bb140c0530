from django.db import models
from django.contrib.auth.models import User


class AdminUser(models.Model):
    """管理员用户扩展"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    phone = models.CharField(max_length=11, null=True, blank=True, verbose_name='手机号')
    department = models.CharField(max_length=50, null=True, blank=True, verbose_name='部门')
    position = models.CharField(max_length=50, null=True, blank=True, verbose_name='职位')
    is_super_admin = models.BooleanField(default=False, verbose_name='超级管理员')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '管理员用户'
        verbose_name_plural = '管理员用户'

    def __str__(self):
        return f"{self.user.username} - {self.position or '管理员'}"


class OperationLog(models.Model):
    """操作日志"""
    ACTION_CHOICES = [
        ('create', '创建'),
        ('update', '更新'),
        ('delete', '删除'),
        ('login', '登录'),
        ('logout', '退出'),
    ]

    admin_user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='操作人')
    action = models.CharField(max_length=20, choices=ACTION_CHOICES, verbose_name='操作类型')
    target_model = models.CharField(max_length=50, null=True, blank=True, verbose_name='目标模型')
    target_id = models.CharField(max_length=50, null=True, blank=True, verbose_name='目标ID')
    description = models.TextField(verbose_name='操作描述')
    ip_address = models.GenericIPAddressField(verbose_name='IP地址')
    user_agent = models.TextField(null=True, blank=True, verbose_name='用户代理')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='操作时间')

    class Meta:
        verbose_name = '操作日志'
        verbose_name_plural = '操作日志'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.admin_user.username} - {self.get_action_display()} - {self.created_at.strftime('%Y-%m-%d %H:%M:%S')}"

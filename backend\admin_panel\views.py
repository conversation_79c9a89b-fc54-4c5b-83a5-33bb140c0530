from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from .models import AdminUser, OperationLog


def is_admin(user):
    """检查是否为管理员"""
    return user.is_authenticated and (user.is_staff or user.is_superuser)


def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def admin_register(request):
    """管理员注册"""
    if request.method == 'POST':
        username = request.POST.get('username')
        email = request.POST.get('email')
        password = request.POST.get('password')
        confirm_password = request.POST.get('confirm_password')

        # 验证
        if not all([username, email, password, confirm_password]):
            messages.error(request, '请填写完整信息')
            return render(request, 'admin_panel/register.html')

        if password != confirm_password:
            messages.error(request, '两次密码不一致')
            return render(request, 'admin_panel/register.html')

        if len(password) < 6:
            messages.error(request, '密码长度至少6位')
            return render(request, 'admin_panel/register.html')

        if User.objects.filter(username=username).exists():
            messages.error(request, '用户名已存在')
            return render(request, 'admin_panel/register.html')

        if User.objects.filter(email=email).exists():
            messages.error(request, '邮箱已被注册')
            return render(request, 'admin_panel/register.html')

        # 创建管理员用户
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            is_staff=True,  # 设置为员工，可以访问admin
            is_superuser=True  # 设置为超级用户
        )

        # 创建管理员资料
        AdminUser.objects.create(
            user=user,
            is_super_admin=True
        )

        # 记录操作日志
        OperationLog.objects.create(
            admin_user=user,
            action='create',
            target_model='AdminUser',
            target_id=str(user.id),
            description=f'注册管理员账号: {username}',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        messages.success(request, '管理员账号注册成功，请登录')
        return redirect('admin_panel:login')

    return render(request, 'admin_panel/register.html')


def admin_login(request):
    """管理员登录"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(request, username=username, password=password)
        if user and (user.is_staff or user.is_superuser):
            login(request, user)

            # 记录登录日志
            OperationLog.objects.create(
                admin_user=user,
                action='login',
                description=f'管理员登录: {username}',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            return redirect('admin_panel:dashboard')
        else:
            messages.error(request, '用户名或密码错误，或者没有管理员权限')

    return render(request, 'admin_panel/login.html')


@user_passes_test(is_admin)
def admin_logout(request):
    """管理员退出"""
    # 记录退出日志
    OperationLog.objects.create(
        admin_user=request.user,
        action='logout',
        description=f'管理员退出: {request.user.username}',
        ip_address=get_client_ip(request),
        user_agent=request.META.get('HTTP_USER_AGENT', '')
    )

    logout(request)
    return redirect('admin_panel:login')


@user_passes_test(is_admin)
def dashboard(request):
    """仪表盘"""
    # 获取统计数据
    total_users = User.objects.filter(is_staff=False).count()
    total_admins = User.objects.filter(is_staff=True).count()
    recent_logs = OperationLog.objects.select_related('admin_user')[:10]

    context = {
        'total_users': total_users,
        'total_admins': total_admins,
        'recent_logs': recent_logs,
    }
    return render(request, 'admin_panel/dashboard.html', context)


@user_passes_test(is_admin)
def category_list(request):
    """商品分类列表"""
    return render(request, 'admin_panel/category_list.html')


@user_passes_test(is_admin)
def product_list(request):
    """商品管理列表"""
    return render(request, 'admin_panel/product_list.html')


@user_passes_test(is_admin)
def order_list(request):
    """订单管理列表"""
    return render(request, 'admin_panel/order_list.html')


@user_passes_test(is_admin)
def user_list(request):
    """用户管理列表"""
    users = User.objects.filter(is_staff=False).order_by('-date_joined')
    context = {
        'users': users,
    }
    return render(request, 'admin_panel/user_list.html', context)


@user_passes_test(is_admin)
def profile(request):
    """个人中心"""
    admin_user, created = AdminUser.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        # 更新个人信息
        phone = request.POST.get('phone')
        department = request.POST.get('department')
        position = request.POST.get('position')

        admin_user.phone = phone
        admin_user.department = department
        admin_user.position = position
        admin_user.save()

        # 更新用户基本信息
        email = request.POST.get('email')
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')

        request.user.email = email
        request.user.first_name = first_name
        request.user.last_name = last_name
        request.user.save()

        # 记录操作日志
        OperationLog.objects.create(
            admin_user=request.user,
            action='update',
            target_model='AdminUser',
            target_id=str(admin_user.id),
            description=f'更新个人信息',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        messages.success(request, '个人信息更新成功')
        return redirect('admin_panel:profile')

    context = {
        'admin_user': admin_user,
    }
    return render(request, 'admin_panel/profile.html', context)

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib import messages
import json

from .models import <PERSON>t, CartItem, CartManager
from products.models import Product


@login_required
def cart_view(request):
    """购物车页面"""
    # 使用静态数据模拟购物车
    cart_items = [
        {
            'id': 1,
            'product': {
                'id': 1,
                'name': '智能手表 Pro',
                'price': 1299.00,
                'image': 'https://picsum.photos/400/400?random=10',
                'stock': 50
            },
            'quantity': 2,
            'total_price': 2598.00,
            'is_selected': True,
            'is_available': True
        },
        {
            'id': 2,
            'product': {
                'id': 2,
                'name': '无线降噪耳机',
                'price': 899.00,
                'image': 'https://picsum.photos/400/400?random=11',
                'stock': 30
            },
            'quantity': 1,
            'total_price': 899.00,
            'is_selected': True,
            'is_available': True
        },
        {
            'id': 3,
            'product': {
                'id': 5,
                'name': 'iPhone 15 Pro',
                'price': 8999.00,
                'image': 'https://picsum.photos/400/400?random=14',
                'stock': 10
            },
            'quantity': 1,
            'total_price': 8999.00,
            'is_selected': False,
            'is_available': True
        }
    ]

    # 计算优惠
    total_amount = sum(item['total_price'] for item in cart_items if item['is_selected'])
    discount_amount = calculate_discount(total_amount)
    final_amount = total_amount - discount_amount

    context = {
        'cart_items': cart_items,
        'total_amount': total_amount,
        'discount_amount': discount_amount,
        'final_amount': final_amount,
        'total_items': len(cart_items),
        'selected_items': len([item for item in cart_items if item['is_selected']])
    }
    return render(request, 'cart/view.html', context)


@login_required
@csrf_exempt
def add_to_cart(request):
    """添加商品到购物车"""
    if request.method == 'POST':
        data = json.loads(request.body)
        product_id = data.get('product_id')
        quantity = int(data.get('quantity', 1))
        
        try:
            product = Product.objects.get(id=product_id, is_active=True)
            success, message = CartManager.add_product(request.user, product, quantity)
            
            if success:
                # 获取购物车摘要
                summary = CartManager.get_cart_summary(request.user)
                return JsonResponse({
                    'success': True, 
                    'message': message,
                    'cart_summary': summary
                })
            else:
                return JsonResponse({'success': False, 'message': message})
                
        except Product.DoesNotExist:
            return JsonResponse({'success': False, 'message': '商品不存在'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
@csrf_exempt
def update_cart_item(request):
    """更新购物车商品"""
    if request.method == 'POST':
        data = json.loads(request.body)
        product_id = data.get('product_id')
        quantity = int(data.get('quantity', 1))
        
        try:
            product = Product.objects.get(id=product_id)
            success, message = CartManager.update_quantity(request.user, product, quantity)
            
            if success:
                summary = CartManager.get_cart_summary(request.user)
                return JsonResponse({
                    'success': True, 
                    'message': message,
                    'cart_summary': summary
                })
            else:
                return JsonResponse({'success': False, 'message': message})
                
        except Product.DoesNotExist:
            return JsonResponse({'success': False, 'message': '商品不存在'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
@csrf_exempt
def remove_cart_item(request):
    """移除购物车商品"""
    if request.method == 'POST':
        data = json.loads(request.body)
        product_id = data.get('product_id')
        
        try:
            product = Product.objects.get(id=product_id)
            success, message = CartManager.remove_product(request.user, product)
            
            if success:
                summary = CartManager.get_cart_summary(request.user)
                return JsonResponse({
                    'success': True, 
                    'message': message,
                    'cart_summary': summary
                })
            else:
                return JsonResponse({'success': False, 'message': message})
                
        except Product.DoesNotExist:
            return JsonResponse({'success': False, 'message': '商品不存在'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
@csrf_exempt
def toggle_cart_item_selection(request):
    """切换购物车商品选中状态"""
    if request.method == 'POST':
        data = json.loads(request.body)
        product_id = data.get('product_id')
        
        try:
            product = Product.objects.get(id=product_id)
            success, message = CartManager.toggle_selection(request.user, product)
            
            if success:
                summary = CartManager.get_cart_summary(request.user)
                return JsonResponse({
                    'success': True, 
                    'message': message,
                    'cart_summary': summary
                })
            else:
                return JsonResponse({'success': False, 'message': message})
                
        except Product.DoesNotExist:
            return JsonResponse({'success': False, 'message': '商品不存在'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
@csrf_exempt
def select_all_cart_items(request):
    """全选/取消全选购物车商品"""
    if request.method == 'POST':
        data = json.loads(request.body)
        selected = data.get('selected', True)
        
        success, message = CartManager.select_all(request.user, selected)
        
        if success:
            summary = CartManager.get_cart_summary(request.user)
            return JsonResponse({
                'success': True, 
                'message': message,
                'cart_summary': summary
            })
        else:
            return JsonResponse({'success': False, 'message': message})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
@csrf_exempt
def clear_cart(request):
    """清空购物车"""
    if request.method == 'POST':
        try:
            cart = Cart.objects.get(user=request.user)
            cart.clear()
            return JsonResponse({'success': True, 'message': '购物车已清空'})
        except Cart.DoesNotExist:
            return JsonResponse({'success': False, 'message': '购物车不存在'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
def cart_summary(request):
    """获取购物车摘要（AJAX）"""
    summary = CartManager.get_cart_summary(request.user)
    return JsonResponse(summary)


@login_required
def checkout(request):
    """结算页面"""
    try:
        cart = Cart.objects.get(user=request.user)
        selected_items = cart.selected_items.select_related('product')
        
        if not selected_items.exists():
            messages.error(request, '请选择要结算的商品')
            return redirect('cart:view')
        
        # 检查库存
        for item in selected_items:
            if not item.is_available:
                messages.error(request, f'商品 {item.product.name} 库存不足或已下架')
                return redirect('cart:view')
        
        # 计算金额
        total_amount = sum(item.total_price for item in selected_items)
        discount_amount = calculate_discount(total_amount)
        final_amount = total_amount - discount_amount
        
        # 获取用户地址
        from users.models import Address
        addresses = Address.objects.filter(user=request.user)
        
        context = {
            'selected_items': selected_items,
            'addresses': addresses,
            'total_amount': total_amount,
            'discount_amount': discount_amount,
            'final_amount': final_amount,
        }
        return render(request, 'cart/checkout.html', context)
        
    except Cart.DoesNotExist:
        messages.error(request, '购物车为空')
        return redirect('cart:view')


def calculate_discount(amount):
    """计算优惠金额"""
    if amount >= 1000:
        return 200
    elif amount >= 300:
        return 50
    elif amount >= 100:
        return 10
    return 0


@login_required
def mini_cart(request):
    """迷你购物车（用于页面头部显示）"""
    try:
        cart = Cart.objects.get(user=request.user)
        recent_items = cart.items.select_related('product')[:5]
        summary = CartManager.get_cart_summary(request.user)
    except Cart.DoesNotExist:
        recent_items = []
        summary = CartManager.get_cart_summary(request.user)
    
    context = {
        'recent_items': recent_items,
        'summary': summary,
    }
    return render(request, 'cart/mini_cart.html', context)

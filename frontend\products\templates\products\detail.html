{% extends 'base.html' %}

{% block title %}{{ product.name|default:"商品详情" }} - 智能电商{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- 面包屑导航 -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'products:index' %}" class="text-gray-700 hover:text-orange-primary">
                    <i class="fa fa-home mr-2"></i>首页
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fa fa-angle-right text-gray-400 mx-2"></i>
                    <a href="{% url 'products:search' %}" class="text-gray-700 hover:text-orange-primary">商品</a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fa fa-angle-right text-gray-400 mx-2"></i>
                    <span class="text-gray-500">智能手表 Pro</span>
                </div>
            </li>
        </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- 商品图片 -->
        <div class="space-y-4">
            <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                <img src="https://picsum.photos/600/600?random=10" alt="智能手表 Pro" class="w-full h-full object-cover">
            </div>
            <div class="grid grid-cols-4 gap-2">
                <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer border-2 border-orange-primary">
                    <img src="https://picsum.photos/150/150?random=10" alt="图片1" class="w-full h-full object-cover">
                </div>
                <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:border-orange-primary border-2 border-transparent">
                    <img src="https://picsum.photos/150/150?random=11" alt="图片2" class="w-full h-full object-cover">
                </div>
                <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:border-orange-primary border-2 border-transparent">
                    <img src="https://picsum.photos/150/150?random=12" alt="图片3" class="w-full h-full object-cover">
                </div>
                <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:border-orange-primary border-2 border-transparent">
                    <img src="https://picsum.photos/150/150?random=13" alt="图片4" class="w-full h-full object-cover">
                </div>
            </div>
        </div>

        <!-- 商品信息 -->
        <div class="space-y-6">
            <div>
                <div class="flex items-center space-x-2 mb-2">
                    <span class="bg-orange-primary text-white text-xs px-2 py-1 rounded">热销</span>
                    <span class="bg-green-500 text-white text-xs px-2 py-1 rounded">新品</span>
                </div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">智能手表 Pro</h1>
                <p class="text-gray-600">健康监测、运动追踪、消息提醒，功能强大的智能手表</p>
            </div>

            <!-- 评分 -->
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <div class="flex text-yellow-400">
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star"></i>
                        <i class="fa fa-star-half-o"></i>
                    </div>
                    <span class="ml-2 text-gray-600">4.5</span>
                </div>
                <span class="text-gray-400">|</span>
                <span class="text-gray-600">128条评价</span>
                <span class="text-gray-400">|</span>
                <span class="text-gray-600">已售1,234件</span>
            </div>

            <!-- 价格 -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-baseline space-x-4">
                    <span class="text-3xl font-bold text-orange-primary">¥1,299</span>
                    <span class="text-lg text-gray-400 line-through">¥1,599</span>
                    <span class="bg-red-100 text-red-600 text-sm px-2 py-1 rounded">省¥300</span>
                </div>
                <div class="mt-2 text-sm text-gray-600">
                    <span>会员价：</span>
                    <span class="text-orange-primary font-semibold">¥1,234</span>
                </div>
            </div>

            <!-- 规格选择 -->
            <div class="space-y-4">
                <div>
                    <h3 class="text-sm font-medium text-gray-900 mb-2">颜色</h3>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 border-2 border-orange-primary bg-orange-50 text-orange-primary rounded-lg text-sm font-medium">
                            黑色
                        </button>
                        <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:border-orange-primary">
                            白色
                        </button>
                        <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:border-orange-primary">
                            银色
                        </button>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-sm font-medium text-gray-900 mb-2">尺寸</h3>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 border-2 border-orange-primary bg-orange-50 text-orange-primary rounded-lg text-sm font-medium">
                            42mm
                        </button>
                        <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:border-orange-primary">
                            46mm
                        </button>
                    </div>
                </div>
            </div>

            <!-- 数量选择 -->
            <div>
                <h3 class="text-sm font-medium text-gray-900 mb-2">数量</h3>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center border border-gray-300 rounded-lg">
                        <button class="px-3 py-2 hover:bg-gray-50" onclick="decreaseQuantity()">
                            <i class="fa fa-minus"></i>
                        </button>
                        <span class="px-4 py-2 border-x border-gray-300" id="quantity">1</span>
                        <button class="px-3 py-2 hover:bg-gray-50" onclick="increaseQuantity()">
                            <i class="fa fa-plus"></i>
                        </button>
                    </div>
                    <span class="text-gray-600">库存：50件</span>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="space-y-3">
                <button class="w-full bg-orange-primary hover:bg-orange-secondary text-white py-3 rounded-lg font-medium transition-colors" onclick="addToCart()">
                    <i class="fa fa-shopping-cart mr-2"></i>加入购物车
                </button>
                <button class="w-full bg-red-500 hover:bg-red-600 text-white py-3 rounded-lg font-medium transition-colors" onclick="buyNow()">
                    <i class="fa fa-bolt mr-2"></i>立即购买
                </button>
                <div class="flex space-x-2">
                    <button class="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50 transition-colors" onclick="toggleFavorite()">
                        <i class="fa fa-heart-o mr-2"></i>收藏
                    </button>
                    <button class="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fa fa-share mr-2"></i>分享
                    </button>
                </div>
            </div>

            <!-- 服务保障 -->
            <div class="bg-blue-50 rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-2">服务保障</h3>
                <div class="grid grid-cols-2 gap-2 text-sm text-gray-600">
                    <div class="flex items-center">
                        <i class="fa fa-check-circle text-green-500 mr-2"></i>
                        正品保证
                    </div>
                    <div class="flex items-center">
                        <i class="fa fa-truck text-blue-500 mr-2"></i>
                        免费配送
                    </div>
                    <div class="flex items-center">
                        <i class="fa fa-refresh text-orange-primary mr-2"></i>
                        7天无理由退货
                    </div>
                    <div class="flex items-center">
                        <i class="fa fa-shield text-purple-500 mr-2"></i>
                        一年质保
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 商品详情和评价 -->
    <div class="mt-12">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
                <button class="py-2 px-1 border-b-2 border-orange-primary text-orange-primary font-medium text-sm" onclick="showTab('details')">
                    商品详情
                </button>
                <button class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm" onclick="showTab('reviews')">
                    用户评价 (128)
                </button>
                <button class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm" onclick="showTab('qa')">
                    常见问题
                </button>
            </nav>
        </div>

        <!-- 商品详情内容 -->
        <div id="details" class="py-8">
            <div class="prose max-w-none">
                <h3>产品特色</h3>
                <ul>
                    <li>全天候健康监测，心率、血氧、睡眠质量实时跟踪</li>
                    <li>100+种运动模式，专业运动数据分析</li>
                    <li>智能消息提醒，重要信息不错过</li>
                    <li>超长续航，正常使用7天，重度使用3天</li>
                    <li>50米防水，游泳佩戴无忧</li>
                </ul>
                
                <h3>技术参数</h3>
                <table class="min-w-full">
                    <tbody>
                        <tr class="border-b">
                            <td class="py-2 font-medium">屏幕尺寸</td>
                            <td class="py-2">1.43英寸AMOLED</td>
                        </tr>
                        <tr class="border-b">
                            <td class="py-2 font-medium">分辨率</td>
                            <td class="py-2">466×466像素</td>
                        </tr>
                        <tr class="border-b">
                            <td class="py-2 font-medium">电池容量</td>
                            <td class="py-2">455mAh</td>
                        </tr>
                        <tr class="border-b">
                            <td class="py-2 font-medium">防水等级</td>
                            <td class="py-2">5ATM</td>
                        </tr>
                        <tr class="border-b">
                            <td class="py-2 font-medium">连接方式</td>
                            <td class="py-2">蓝牙5.2、Wi-Fi</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 用户评价内容 -->
        <div id="reviews" class="py-8 hidden">
            <div class="space-y-6">
                <!-- 评价统计 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-orange-primary">4.5</div>
                            <div class="flex justify-center text-yellow-400 mt-1">
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star-half-o"></i>
                            </div>
                            <div class="text-sm text-gray-600 mt-1">128条评价</div>
                        </div>
                        <div class="col-span-2">
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <span class="text-sm w-8">5星</span>
                                    <div class="flex-1 bg-gray-200 rounded-full h-2 mx-2">
                                        <div class="bg-yellow-400 h-2 rounded-full" style="width: 70%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600">70%</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm w-8">4星</span>
                                    <div class="flex-1 bg-gray-200 rounded-full h-2 mx-2">
                                        <div class="bg-yellow-400 h-2 rounded-full" style="width: 20%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600">20%</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm w-8">3星</span>
                                    <div class="flex-1 bg-gray-200 rounded-full h-2 mx-2">
                                        <div class="bg-yellow-400 h-2 rounded-full" style="width: 8%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600">8%</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm w-8">2星</span>
                                    <div class="flex-1 bg-gray-200 rounded-full h-2 mx-2">
                                        <div class="bg-yellow-400 h-2 rounded-full" style="width: 2%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600">2%</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm w-8">1星</span>
                                    <div class="flex-1 bg-gray-200 rounded-full h-2 mx-2">
                                        <div class="bg-yellow-400 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 评价列表 -->
                <div class="space-y-4">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                <i class="fa fa-user text-gray-600"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-1">
                                    <span class="font-medium">用户***123</span>
                                    <div class="flex text-yellow-400">
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                    </div>
                                </div>
                                <p class="text-gray-700 mb-2">手表很不错，功能齐全，续航也很好，值得推荐！</p>
                                <div class="flex space-x-2 mb-2">
                                    <img src="https://picsum.photos/80/80?random=20" alt="评价图片" class="w-16 h-16 object-cover rounded">
                                    <img src="https://picsum.photos/80/80?random=21" alt="评价图片" class="w-16 h-16 object-cover rounded">
                                </div>
                                <div class="text-sm text-gray-500">2024-01-15</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 常见问题内容 -->
        <div id="qa" class="py-8 hidden">
            <div class="space-y-4">
                <div class="border border-gray-200 rounded-lg p-4">
                    <h4 class="font-medium text-gray-900 mb-2">Q: 这款手表支持哪些运动模式？</h4>
                    <p class="text-gray-700">A: 支持100+种运动模式，包括跑步、骑行、游泳、瑜伽、篮球等常见运动，以及登山、滑雪等户外运动。</p>
                </div>
                <div class="border border-gray-200 rounded-lg p-4">
                    <h4 class="font-medium text-gray-900 mb-2">Q: 续航时间有多长？</h4>
                    <p class="text-gray-700">A: 正常使用模式下可续航7天，重度使用（GPS常开、频繁通知）约3天，省电模式可达14天。</p>
                </div>
                <div class="border border-gray-200 rounded-lg p-4">
                    <h4 class="font-medium text-gray-900 mb-2">Q: 是否支持独立通话？</h4>
                    <p class="text-gray-700">A: 支持蓝牙通话功能，需要与手机连接。暂不支持独立插卡通话。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 推荐商品 -->
    <div class="mt-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">相关推荐</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- 推荐商品卡片 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                <div class="relative">
                    <a href="{% url 'products:detail' 2 %}">
                        <img src="https://picsum.photos/300/300?random=30" alt="推荐商品" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                    </a>
                    <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                        <i class="fa fa-heart-o"></i>
                    </button>
                </div>
                <div class="p-4">
                    <h3 class="font-medium text-gray-800 mb-1">无线降噪耳机</h3>
                    <div class="flex justify-between items-center">
                        <span class="text-orange-primary font-bold">¥899</span>
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-shopping-cart text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let quantity = 1;

function increaseQuantity() {
    quantity++;
    document.getElementById('quantity').textContent = quantity;
}

function decreaseQuantity() {
    if (quantity > 1) {
        quantity--;
        document.getElementById('quantity').textContent = quantity;
    }
}

function addToCart() {
    if (!isLoggedIn()) {
        alert('请先登录');
        window.location.href = '{% url "users:login" %}';
        return;
    }
    alert(`已添加${quantity}件商品到购物车`);
}

function buyNow() {
    if (!isLoggedIn()) {
        alert('请先登录');
        window.location.href = '{% url "users:login" %}';
        return;
    }
    alert('跳转到结算页面');
}

function toggleFavorite() {
    if (!isLoggedIn()) {
        alert('请先登录');
        window.location.href = '{% url "users:login" %}';
        return;
    }
    alert('收藏功能');
}

function showTab(tabName) {
    // 隐藏所有内容
    document.getElementById('details').classList.add('hidden');
    document.getElementById('reviews').classList.add('hidden');
    document.getElementById('qa').classList.add('hidden');
    
    // 显示选中的内容
    document.getElementById(tabName).classList.remove('hidden');
    
    // 更新标签样式
    document.querySelectorAll('nav button').forEach(btn => {
        btn.classList.remove('border-orange-primary', 'text-orange-primary');
        btn.classList.add('border-transparent', 'text-gray-500');
    });
    
    event.target.classList.remove('border-transparent', 'text-gray-500');
    event.target.classList.add('border-orange-primary', 'text-orange-primary');
}

function isLoggedIn() {
    return {% if user.is_authenticated %}true{% else %}false{% endif %};
}

// 图片切换
document.querySelectorAll('.grid img').forEach((img, index) => {
    img.addEventListener('click', function() {
        // 移除所有边框
        document.querySelectorAll('.grid > div').forEach(div => {
            div.classList.remove('border-orange-primary');
            div.classList.add('border-transparent');
        });
        
        // 添加当前边框
        this.parentElement.classList.remove('border-transparent');
        this.parentElement.classList.add('border-orange-primary');
        
        // 更换主图
        document.querySelector('.aspect-square img').src = this.src.replace('150', '600');
    });
});
</script>
{% endblock %}

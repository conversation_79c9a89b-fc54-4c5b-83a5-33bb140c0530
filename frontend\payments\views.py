from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.contrib import messages
from .models import Payment, PaymentLog


@login_required
def payment_page(request, order_id):
    """支付页面"""
    from orders.models import Order
    order = get_object_or_404(Order, id=order_id, user=request.user, status='pending')
    
    context = {
        'order': order,
    }
    return render(request, 'payments/payment.html', context)


@login_required
def create_payment(request):
    """创建支付"""
    if request.method == 'POST':
        order_id = request.POST.get('order_id')
        payment_method = request.POST.get('payment_method')
        
        from orders.models import Order
        order = get_object_or_404(Order, id=order_id, user=request.user, status='pending')
        
        # 创建支付记录
        payment = Payment.objects.create(
            payment_no=f"PAY{order.order_no}",
            order=order,
            user=request.user,
            payment_method=payment_method,
            amount=order.final_amount
        )
        
        # 根据支付方式处理
        if payment_method == 'alipay':
            return redirect('payments:alipay', payment_id=payment.id)
        elif payment_method == 'balance':
            return redirect('payments:balance_pay', payment_id=payment.id)
        
    return JsonResponse({'success': False, 'message': '支付方式不支持'})


def payment_success(request):
    """支付成功页面"""
    return render(request, 'payments/success.html')


def payment_failed(request):
    """支付失败页面"""
    return render(request, 'payments/failed.html')

{% extends 'base.html' %}

{% block title %}用户登录 - 智能电商{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-orange-100">
                <i class="fa fa-user text-orange-primary text-xl"></i>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                登录您的账户
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                还没有账户？
                <a href="{% url 'users:register' %}" class="font-medium text-orange-primary hover:text-orange-secondary">
                    立即注册
                </a>
            </p>
        </div>
        
        <form class="mt-8 space-y-6" method="post">
            {% csrf_token %}
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="username" class="sr-only">用户名</label>
                    <input id="username" name="username" type="text" required 
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-orange-primary focus:border-orange-primary focus:z-10 sm:text-sm" 
                           placeholder="用户名">
                </div>
                <div>
                    <label for="password" class="sr-only">密码</label>
                    <input id="password" name="password" type="password" required 
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-orange-primary focus:border-orange-primary focus:z-10 sm:text-sm" 
                           placeholder="密码">
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="remember-me" name="remember-me" type="checkbox" 
                           class="h-4 w-4 text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                    <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                        记住我
                    </label>
                </div>

                <div class="text-sm">
                    <a href="#" class="font-medium text-orange-primary hover:text-orange-secondary">
                        忘记密码？
                    </a>
                </div>
            </div>

            <div>
                <button type="submit" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-orange-primary hover:bg-orange-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-primary">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fa fa-lock text-orange-secondary group-hover:text-orange-primary"></i>
                    </span>
                    登录
                </button>
            </div>

            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-gray-50 text-gray-500">或者</span>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-2 gap-3">
                    <a href="#" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i class="fa fa-wechat text-green-500"></i>
                        <span class="ml-2">微信</span>
                    </a>
                    <a href="#" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i class="fa fa-qq text-blue-500"></i>
                        <span class="ml-2">QQ</span>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        if (!username.trim()) {
            alert('请输入用户名');
            e.preventDefault();
            return;
        }
        
        if (!password.trim()) {
            alert('请输入密码');
            e.preventDefault();
            return;
        }
    });
});
</script>
{% endblock %}

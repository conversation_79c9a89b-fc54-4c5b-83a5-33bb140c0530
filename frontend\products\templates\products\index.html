{% extends 'base.html' %}

{% block title %}智能电商 - 首页{% endblock %}

{% block content %}
<!-- 轮播图 -->
<section class="relative overflow-hidden">
    <div id="carousel" class="flex transition-transform duration-500 ease-in-out">
        <div class="w-full flex-shrink-0 relative">
            <img src="https://picsum.photos/1600/500?random=1" alt="夏季大促销" class="w-full h-[500px] object-cover">
            <div class="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex items-center">
                <div class="container mx-auto px-4 text-white max-w-xl">
                    <h2 class="text-[clamp(2rem,5vw,3.5rem)] font-bold leading-tight mb-4">夏季大促销</h2>
                    <p class="text-[clamp(1rem,2vw,1.25rem)] mb-6">限时折扣，全场低至3折，快来选购吧！</p>
                    <button class="bg-orange-primary hover:bg-orange-secondary text-white py-3 px-8 rounded-full font-medium transition-colors">立即购买</button>
                </div>
            </div>
        </div>
        <div class="w-full flex-shrink-0 relative">
            <img src="https://picsum.photos/1600/500?random=2" alt="新品上市" class="w-full h-[500px] object-cover">
            <div class="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex items-center">
                <div class="container mx-auto px-4 text-white max-w-xl">
                    <h2 class="text-[clamp(2rem,5vw,3.5rem)] font-bold leading-tight mb-4">新品上市</h2>
                    <p class="text-[clamp(1rem,2vw,1.25rem)] mb-6">2025最新款商品，时尚潮流，品质保证！</p>
                    <button class="bg-orange-primary hover:bg-orange-secondary text-white py-3 px-8 rounded-full font-medium transition-colors">查看详情</button>
                </div>
            </div>
        </div>
        <div class="w-full flex-shrink-0 relative">
            <img src="https://picsum.photos/1600/500?random=3" alt="会员专享" class="w-full h-[500px] object-cover">
            <div class="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex items-center">
                <div class="container mx-auto px-4 text-white max-w-xl">
                    <h2 class="text-[clamp(2rem,5vw,3.5rem)] font-bold leading-tight mb-4">会员专享</h2>
                    <p class="text-[clamp(1rem,2vw,1.25rem)] mb-6">成为会员，享受专属优惠和贴心服务！</p>
                    <button class="bg-orange-primary hover:bg-orange-secondary text-white py-3 px-8 rounded-full font-medium transition-colors">立即加入</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 轮播图导航按钮 -->
    <button id="prev-btn" class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/70 hover:bg-white text-orange-primary w-10 h-10 rounded-full flex items-center justify-center transition-colors">
        <i class="fa fa-angle-left text-xl"></i>
    </button>
    <button id="next-btn" class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/70 hover:bg-white text-orange-primary w-10 h-10 rounded-full flex items-center justify-center transition-colors">
        <i class="fa fa-angle-right text-xl"></i>
    </button>

    <!-- 轮播图指示器 -->
    <div class="absolute bottom-6 left-0 right-0 flex justify-center space-x-2">
        <button class="w-3 h-3 rounded-full bg-white/50 hover:bg-white focus:outline-none carousel-indicator active" data-index="0"></button>
        <button class="w-3 h-3 rounded-full bg-white/50 hover:bg-white focus:outline-none carousel-indicator" data-index="1"></button>
        <button class="w-3 h-3 rounded-full bg-white/50 hover:bg-white focus:outline-none carousel-indicator" data-index="2"></button>
    </div>
</section>

<!-- 分类导航 -->
<section class="py-12 bg-white">
    <div class="container mx-auto px-4">
        <h2 class="text-2xl font-bold text-gray-800 mb-8 text-center">商品分类</h2>
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <a href="{% url 'products:search' %}?category=11" class="group">
                <div class="bg-gray-50 rounded-xl p-6 text-center hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-orange-200 transition-colors">
                        <i class="fa fa-mobile text-orange-primary text-2xl"></i>
                    </div>
                    <h3 class="font-medium text-gray-800">手机通讯</h3>
                </div>
            </a>
            <a href="{% url 'products:search' %}?category=12" class="group">
                <div class="bg-gray-50 rounded-xl p-6 text-center hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                        <i class="fa fa-laptop text-purple-600 text-2xl"></i>
                    </div>
                    <h3 class="font-medium text-gray-800">电脑办公</h3>
                </div>
            </a>
            <a href="{% url 'products:search' %}?category=13" class="group">
                <div class="bg-gray-50 rounded-xl p-6 text-center hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors">
                        <i class="fa fa-headphones text-green-600 text-2xl"></i>
                    </div>
                    <h3 class="font-medium text-gray-800">数码配件</h3>
                </div>
            </a>
            <a href="{% url 'products:search' %}?category=21" class="group">
                <div class="bg-gray-50 rounded-xl p-6 text-center hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-red-200 transition-colors">
                        <i class="fa fa-male text-red-600 text-2xl"></i>
                    </div>
                    <h3 class="font-medium text-gray-800">男装</h3>
                </div>
            </a>
            <a href="{% url 'products:search' %}?category=22" class="group">
                <div class="bg-gray-50 rounded-xl p-6 text-center hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-yellow-200 transition-colors">
                        <i class="fa fa-female text-yellow-600 text-2xl"></i>
                    </div>
                    <h3 class="font-medium text-gray-800">女装</h3>
                </div>
            </a>
            <a href="{% url 'products:category_list' %}" class="group">
                <div class="bg-gray-50 rounded-xl p-6 text-center hover:shadow-md transition-shadow">
                    <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-indigo-200 transition-colors">
                        <i class="fa fa-ellipsis-h text-indigo-600 text-2xl"></i>
                    </div>
                    <h3 class="font-medium text-gray-800">更多分类</h3>
                </div>
            </a>
        </div>
    </div>
</section>

<!-- 热门推荐 -->
<section class="py-12 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="flex justify-between items-center mb-8">
            <h2 class="text-2xl font-bold text-gray-800">热门推荐</h2>
            <a href="{% url 'products:list' %}" class="text-orange-primary hover:text-orange-secondary flex items-center">
                查看全部 <i class="fa fa-angle-right ml-1"></i>
            </a>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- 商品卡片1 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                <div class="relative">
                    <a href="{% url 'products:detail' 1 %}">
                        <img src="https://picsum.photos/400/400?random=10" alt="智能手表" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                    </a>
                    <div class="absolute top-3 left-3">
                        <span class="bg-orange-primary text-white text-xs font-medium py-1 px-2 rounded">热销</span>
                    </div>
                    <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors" onclick="toggleFavorite(1)">
                        <i class="fa fa-heart-o"></i>
                    </button>
                </div>
                <div class="p-4">
                    <div class="flex items-center mb-2">
                        <div class="flex text-yellow-400">
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star-half-o"></i>
                        </div>
                        <span class="text-gray-500 text-sm ml-2">4.5 (128)</span>
                    </div>
                    <h3 class="font-medium text-gray-800 mb-1">
                        <a href="{% url 'products:detail' 1 %}" class="hover:text-orange-primary transition-colors">智能手表 Pro</a>
                    </h3>
                    <p class="text-gray-500 text-sm mb-3">健康监测、运动追踪、消息提醒，功能强大的智能手表。</p>
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="text-orange-primary font-bold">¥1,299</span>
                            <span class="text-gray-400 line-through text-sm ml-2">¥1,599</span>
                        </div>
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white w-10 h-10 rounded-full flex items-center justify-center transition-colors" onclick="addToCart(1)">
                            <i class="fa fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 商品卡片2 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                <div class="relative">
                    <a href="{% url 'products:detail' 2 %}">
                        <img src="https://picsum.photos/400/400?random=11" alt="无线耳机" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                    </a>
                    <div class="absolute top-3 left-3">
                        <span class="bg-green-500 text-white text-xs font-medium py-1 px-2 rounded">新品</span>
                    </div>
                    <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors" onclick="toggleFavorite(2)">
                        <i class="fa fa-heart-o"></i>
                    </button>
                </div>
                <div class="p-4">
                    <div class="flex items-center mb-2">
                        <div class="flex text-yellow-400">
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star-o"></i>
                        </div>
                        <span class="text-gray-500 text-sm ml-2">4.0 (86)</span>
                    </div>
                    <h3 class="font-medium text-gray-800 mb-1">
                        <a href="{% url 'products:detail' 2 %}" class="hover:text-orange-primary transition-colors">无线降噪耳机</a>
                    </h3>
                    <p class="text-gray-500 text-sm mb-3">主动降噪，高清音质，舒适佩戴，长达30小时续航。</p>
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="text-orange-primary font-bold">¥899</span>
                            <span class="text-gray-400 line-through text-sm ml-2">¥1,099</span>
                        </div>
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white w-10 h-10 rounded-full flex items-center justify-center transition-colors" onclick="addToCart(2)">
                            <i class="fa fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 商品卡片3 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                <div class="relative">
                    <img src="https://picsum.photos/400/400?random=12" alt="运动背包" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                    <div class="absolute top-3 left-3">
                        <span class="bg-orange-500 text-white text-xs font-medium py-1 px-2 rounded">促销</span>
                    </div>
                    <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                        <i class="fa fa-heart-o"></i>
                    </button>
                </div>
                <div class="p-4">
                    <div class="flex items-center mb-2">
                        <div class="flex text-yellow-400">
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                        </div>
                        <span class="text-gray-500 text-sm ml-2">5.0 (42)</span>
                    </div>
                    <h3 class="font-medium text-gray-800 mb-1">专业运动背包</h3>
                    <p class="text-gray-500 text-sm mb-3">大容量，多隔层设计，防水耐磨，适合各种户外运动。</p>
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="text-orange-primary font-bold">¥299</span>
                            <span class="text-gray-400 line-through text-sm ml-2">¥399</span>
                        </div>
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white w-10 h-10 rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 商品卡片4 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                <div class="relative">
                    <img src="https://picsum.photos/400/400?random=13" alt="智能音箱" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                    <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                        <i class="fa fa-heart-o"></i>
                    </button>
                </div>
                <div class="p-4">
                    <div class="flex items-center mb-2">
                        <div class="flex text-yellow-400">
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star-half-o"></i>
                            <i class="fa fa-star-o"></i>
                        </div>
                        <span class="text-gray-500 text-sm ml-2">3.5 (67)</span>
                    </div>
                    <h3 class="font-medium text-gray-800 mb-1">智能语音助手音箱</h3>
                    <p class="text-gray-500 text-sm mb-3">支持多种语音指令，智能家居控制，高品质音乐播放。</p>
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="text-orange-primary font-bold">¥399</span>
                            <span class="text-gray-400 line-through text-sm ml-2">¥499</span>
                        </div>
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white w-10 h-10 rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-shopping-cart"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// 轮播图功能
let currentSlide = 0;
const slides = document.querySelectorAll('#carousel > div');
const indicators = document.querySelectorAll('.carousel-indicator');
const totalSlides = slides.length;

function showSlide(index) {
    const carousel = document.getElementById('carousel');
    carousel.style.transform = `translateX(-${index * 100}%)`;

    // 更新指示器
    indicators.forEach((indicator, i) => {
        if (i === index) {
            indicator.classList.add('active');
            indicator.style.backgroundColor = 'white';
        } else {
            indicator.classList.remove('active');
            indicator.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
        }
    });

    currentSlide = index;
}

function nextSlide() {
    currentSlide = (currentSlide + 1) % totalSlides;
    showSlide(currentSlide);
}

function prevSlide() {
    currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
    showSlide(currentSlide);
}

// 事件监听器
document.getElementById('next-btn').addEventListener('click', nextSlide);
document.getElementById('prev-btn').addEventListener('click', prevSlide);

indicators.forEach((indicator, index) => {
    indicator.addEventListener('click', () => showSlide(index));
});

// 自动播放
setInterval(nextSlide, 5000);

// 初始化
showSlide(0);

// 商品操作函数
function toggleFavorite(productId) {
    // 收藏功能
    if (!isLoggedIn()) {
        alert('请先登录');
        window.location.href = '{% url "users:login" %}';
        return;
    }
    alert('收藏功能');
}

function addToCart(productId) {
    // 添加到购物车
    if (!isLoggedIn()) {
        alert('请先登录');
        window.location.href = '{% url "users:login" %}';
        return;
    }
    alert('已添加到购物车');
}

function isLoggedIn() {
    // 检查用户是否登录
    return {% if user.is_authenticated %}true{% else %}false{% endif %};
}
</script>
{% endblock %}

from django.urls import path
from . import views

app_name = 'management'

urlpatterns = [
    # 认证
    path('login/', views.admin_login, name='login'),
    path('logout/', views.admin_logout, name='logout'),
    path('register/', views.admin_register, name='register'),
    
    # 仪表盘
    path('dashboard/', views.dashboard, name='dashboard'),
    
    # 用户管理
    path('users/', views.user_list, name='user_list'),
    
    # 商品管理
    path('products/', views.product_list, name='product_list'),
    path('products/add/', views.product_add, name='product_add'),
    path('products/<int:product_id>/edit/', views.product_edit, name='product_edit'),
    path('products/<int:product_id>/delete/', views.product_delete, name='product_delete'),
    
    # 分类管理
    path('categories/', views.category_list, name='category_list'),
    
    # 订单管理
    path('orders/', views.order_list, name='order_list'),
    path('orders/<int:order_id>/', views.order_detail, name='order_detail'),
    path('update-order-status/', views.update_order_status, name='update_order_status'),
]

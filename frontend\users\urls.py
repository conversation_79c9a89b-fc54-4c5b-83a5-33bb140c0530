from django.urls import path
from . import views

app_name = 'users'

urlpatterns = [
    # 用户认证
    path('register/', views.user_register, name='register'),
    path('login/', views.user_login, name='login'),
    path('logout/', views.user_logout, name='logout'),
    
    # 个人中心
    path('profile/', views.user_profile, name='profile'),
    
    # 收货地址管理
    path('addresses/', views.address_list, name='address_list'),
    path('addresses/add/', views.address_add, name='address_add'),
    path('addresses/<int:address_id>/edit/', views.address_edit, name='address_edit'),
    path('addresses/<int:address_id>/delete/', views.address_delete, name='address_delete'),
    path('set-default-address/', views.set_default_address, name='set_default_address'),
    
    # 余额和VIP
    path('balance-log/', views.balance_log, name='balance_log'),
    path('vip-center/', views.vip_center, name='vip_center'),

    # 修改密码
    path('change-password/', views.change_password, name='change_password'),
]

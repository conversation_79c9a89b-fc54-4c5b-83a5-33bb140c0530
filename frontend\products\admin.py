from django.contrib import admin
from .models import (
    Category, Brand, Product, ProductImage, ProductAttribute, 
    ProductAttributeValue, Favorite, Banner
)


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent', 'sort_order', 'is_active', 'product_count', 'created_at']
    list_filter = ['parent', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    list_editable = ['sort_order', 'is_active']
    ordering = ['sort_order', 'name']


@admin.register(Brand)
class BrandAdmin(admin.ModelAdmin):
    list_display = ['name', 'is_active', 'sort_order', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    list_editable = ['is_active', 'sort_order']


class ProductImageInline(admin.TabularInline):
    model = ProductImage
    extra = 1


class ProductAttributeValueInline(admin.TabularInline):
    model = ProductAttributeValue
    extra = 1


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'category', 'brand', 'price', 'stock', 'sales', 
        'is_hot', 'is_new', 'is_active', 'created_at'
    ]
    list_filter = [
        'category', 'brand', 'is_hot', 'is_new', 'is_recommend', 
        'is_active', 'created_at'
    ]
    search_fields = ['name', 'sku', 'description']
    list_editable = ['price', 'stock', 'is_hot', 'is_new', 'is_active']
    readonly_fields = ['sales', 'created_at', 'updated_at']
    inlines = [ProductImageInline, ProductAttributeValueInline]
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'category', 'brand', 'sku', 'description', 'detail')
        }),
        ('价格库存', {
            'fields': ('price', 'original_price', 'cost_price', 'stock', 'sales')
        }),
        ('商品属性', {
            'fields': ('weight', 'length', 'width', 'height')
        }),
        ('状态标签', {
            'fields': ('is_hot', 'is_new', 'is_recommend', 'is_active')
        }),
        ('SEO设置', {
            'fields': ('seo_title', 'seo_keywords', 'seo_description'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ProductImage)
class ProductImageAdmin(admin.ModelAdmin):
    list_display = ['product', 'alt_text', 'sort_order', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['product__name', 'alt_text']
    list_editable = ['sort_order', 'is_active']


@admin.register(ProductAttribute)
class ProductAttributeAdmin(admin.ModelAdmin):
    list_display = ['name', 'is_required', 'sort_order', 'created_at']
    list_filter = ['is_required', 'created_at']
    search_fields = ['name']
    list_editable = ['is_required', 'sort_order']


@admin.register(Favorite)
class FavoriteAdmin(admin.ModelAdmin):
    list_display = ['user', 'product', 'created_at']
    list_filter = ['created_at']
    search_fields = ['user__username', 'product__name']
    raw_id_fields = ['user', 'product']


@admin.register(Banner)
class BannerAdmin(admin.ModelAdmin):
    list_display = ['title', 'sort_order', 'is_active', 'start_time', 'end_time', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['title', 'subtitle']
    list_editable = ['sort_order', 'is_active']

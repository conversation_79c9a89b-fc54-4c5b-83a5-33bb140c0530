# 🛍️ 智能电商系统

一个基于 Django 的完整电商解决方案，包含前台商城和后台管理系统。

## ✨ 系统特色

### 🎨 设计特色
- **主色调**: 橙色 (#FF6B35) 
- **响应式设计**: 完美适配各种设备
- **现代化UI**: Bootstrap 5 + Font Awesome
- **用户体验**: 流畅的交互和动画效果

### 🔄 数据同步
- **共享数据库**: 前后台使用同一 MySQL 数据库
- **实时同步**: 后台修改立即在前台生效
- **数据一致性**: 通过 Django 信号机制保证数据同步

## 🚀 快速开始

### 环境要求
- Python 3.8+
- MySQL 5.7+
- Django 4.2+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd shoping
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置数据库**
- 启动 MySQL 服务
- 创建数据库：`CREATE DATABASE shopping;`
- 确保数据库配置正确（用户名: root, 密码: 123456）

4. **初始化数据库**
```bash
# 运行初始化脚本
init_database.bat
```

5. **启动服务**
```bash
# 运行启动脚本
start_servers.bat
```

### 访问地址
- **前台商城**: http://127.0.0.1:8001/
- **后台管理**: http://127.0.0.1:8003/dashboard/

## 📁 项目结构

```
shoping/
├── frontend/              # 前台商城项目
│   ├── users/            # 用户系统
│   │   ├── models.py     # 用户模型、VIP等级、地址等
│   │   ├── views.py      # 用户认证、个人中心
│   │   └── urls.py       # 用户相关URL
│   ├── products/         # 商品系统
│   │   ├── models.py     # 商品、分类、品牌、收藏等
│   │   ├── views.py      # 商品展示、搜索、收藏
│   │   └── urls.py       # 商品相关URL
│   ├── cart/             # 购物车系统
│   │   ├── models.py     # 购物车、购物车项
│   │   ├── views.py      # 购物车管理
│   │   └── urls.py       # 购物车相关URL
│   ├── orders/           # 订单系统
│   │   ├── models.py     # 订单、订单项、优惠规则
│   │   ├── views.py      # 订单创建、管理
│   │   └── urls.py       # 订单相关URL
│   ├── payments/         # 支付系统
│   │   ├── models.py     # 支付记录、支付日志
│   │   ├── views.py      # 支付处理
│   │   └── urls.py       # 支付相关URL
│   ├── reviews/          # 评价系统
│   │   ├── models.py     # 评价、评价图片
│   │   ├── views.py      # 评价管理
│   │   └── urls.py       # 评价相关URL
│   └── frontend/         # 项目配置
├── backend/              # 后台管理项目
│   ├── admin_panel/      # 管理员系统
│   ├── statistics/       # 数据统计系统
│   ├── settings/         # 系统设置
│   └── backend/          # 项目配置
├── media/                # 媒体文件目录
├── templates/            # 共享模板
├── requirements.txt      # 依赖包列表
├── setup_project.bat     # 完整安装脚本
├── init_database.bat     # 数据库初始化脚本
├── start_servers.bat     # 服务启动脚本
└── README.md            # 项目说明
```

## 🛍️ 前台功能模块

### 👤 用户系统 (users)
- ✅ 用户注册/登录/退出
- ✅ 个人中心管理
- ✅ 个人资料编辑（头像、手机、性别、生日）
- ✅ 收货地址管理（增删改查、默认地址）
- ✅ VIP会员系统（4级会员、折扣权益）
- ✅ 账户余额管理
- ✅ 登录日志记录
- ✅ 余额变动日志

### 🛒 商品系统 (products)
- ✅ 商品展示（热门、新品、推荐）
- ✅ 商品详情页（多图展示、属性参数）
- ✅ 10大主分类 + 30子分类
- ✅ 品牌管理
- ✅ 商品搜索/筛选/排序
- ✅ 商品收藏功能
- ✅ 库存实时管理
- ✅ 轮播图管理
- ✅ SEO优化支持

### 🛍️ 购物车系统 (cart)
- ✅ 添加商品到购物车
- ✅ 购物车商品管理（数量修改/删除）
- ✅ 批量选择功能（全选/取消全选）
- ✅ 实时价格计算
- ✅ 库存验证
- ✅ 迷你购物车显示
- ✅ 购物车摘要统计

### 📦 订单系统 (orders)
- ✅ 订单创建（从购物车）
- ✅ 订单状态管理（7种状态）
- ✅ 订单列表/详情
- ✅ 订单取消/确认收货
- ✅ 满减优惠系统（满100减10等）
- ✅ 物流跟踪
- ✅ 订单状态变更日志

### 💳 支付系统 (payments)
- ✅ 多种支付方式（支付宝、微信、余额）
- ✅ 支付状态跟踪
- ✅ 支付日志记录
- ✅ 支付成功/失败页面
- ✅ 第三方支付集成准备

### ⭐ 评价系统 (reviews)
- ✅ 5星评分系统
- ✅ 文字+图片评价
- ✅ 匿名评价选项
- ✅ 评价审核机制
- ✅ 管理员回复
- ✅ 评价统计分析

## 🔧 后台功能

### 👨‍💼 管理员系统
- ✅ 管理员注册/登录
- ✅ 权限管理
- ✅ 操作日志

### 📊 数据统计
- ✅ 仪表盘概览
- ✅ 用户统计
- ✅ 商品统计
- ✅ 订单统计
- ✅ 销售统计

### 👥 用户管理
- ✅ 用户列表
- ✅ 用户搜索
- ✅ 用户状态管理
- ✅ VIP管理

### 📦 商品管理
- ✅ 商品列表
- ✅ 商品添加/编辑/删除
- ✅ 商品状态管理
- ✅ 商品图片管理
- ✅ 库存管理
- ✅ 热门/新品标签

### 🏷️ 分类管理
- ✅ 分类列表
- ✅ 分类添加/编辑/删除
- ✅ 分类排序
- ✅ 分类图标管理

### 📋 订单管理
- ✅ 订单列表
- ✅ 订单搜索/筛选
- ✅ 订单详情
- ✅ 订单状态更新
- ✅ 发货管理

## 🛡️ 安全特性

### 🔐 权限控制
- ✅ 用户权限系统
- ✅ 管理员权限分级
- ✅ API权限控制
- ✅ CSRF保护

### 🔒 数据安全
- ✅ 密码加密
- ✅ SQL注入防护
- ✅ XSS防护
- ✅ 文件上传安全

## 📱 技术栈

### 🎨 前端技术
- **Bootstrap 5**: 响应式UI框架
- **Font Awesome**: 图标库
- **jQuery**: JavaScript库
- **AJAX**: 异步数据交互
- **CSS3**: 现代样式和动画

### 🔧 后端技术
- **Django 4.2**: Python Web框架
- **MySQL**: 关系型数据库
- **Django REST Framework**: API框架
- **Django Signals**: 信号机制
- **Pillow**: 图片处理

### 📊 数据管理
- **数据库索引**: 查询性能优化
- **分页显示**: 大数据量处理
- **缓存机制**: 数据缓存优化
- **文件管理**: 媒体文件统一管理

## 🎯 系统优势

### 🚀 性能优势
- ⚡ 快速响应
- 🔄 实时同步
- 📱 移动适配
- 🎨 美观界面

### 🛠️ 维护优势
- 🧹 代码规范
- 📝 详细日志
- 🔧 易于扩展
- 🛡️ 安全可靠

### 💼 商业价值
- 💰 完整电商流程
- 📈 数据分析
- 👥 用户管理
- 🎯 营销功能

## 📞 技术支持

如有问题，请联系：
- 📧 Email: <EMAIL>
- 📱 电话: 400-123-4567

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

---

© 2024 智能电商系统. All rights reserved.

# 🛍️ 智能电商系统

一个基于 Django 的完整电商解决方案，包含前台商城和后台管理系统。

## ✨ 系统特色

### 🎨 设计特色
- **主色调**: 橙色 (#FF6B35) 
- **响应式设计**: 完美适配各种设备
- **现代化UI**: Bootstrap 5 + Font Awesome
- **用户体验**: 流畅的交互和动画效果

### 🔄 数据同步
- **共享数据库**: 前后台使用同一 MySQL 数据库
- **实时同步**: 后台修改立即在前台生效
- **数据一致性**: 通过 Django 信号机制保证数据同步

## 🚀 快速开始

### 环境要求
- Python 3.8+
- MySQL 5.7+
- Django 4.2+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd shoping
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置数据库**
- 启动 MySQL 服务
- 创建数据库：`CREATE DATABASE shopping;`
- 确保数据库配置正确（用户名: root, 密码: 123456）

4. **初始化数据库**
```bash
# 运行初始化脚本
init_database.bat
```

5. **启动服务**
```bash
# 运行启动脚本
start_servers.bat
```

### 访问地址
- **前台商城**: http://127.0.0.1:8001/
- **后台管理**: http://127.0.0.1:8003/dashboard/

## 📁 项目结构

```
shoping/
├── frontend/              # 前台商城项目
│   ├── shop/             # 商城应用
│   │   ├── models.py     # 数据模型
│   │   ├── views.py      # 视图函数
│   │   ├── urls.py       # URL配置
│   │   └── templates/    # 模板文件
│   └── frontend/         # 项目配置
├── backend/              # 后台管理项目
│   ├── management/       # 管理应用
│   │   ├── models.py     # 数据模型（与前台共享）
│   │   ├── views.py      # 管理视图
│   │   ├── urls.py       # URL配置
│   │   └── templates/    # 管理模板
│   └── backend/          # 项目配置
├── media/                # 媒体文件目录
├── requirements.txt      # 依赖包列表
├── init_database.bat     # 数据库初始化脚本
├── start_servers.bat     # 服务启动脚本
└── README.md            # 项目说明
```

## 🛍️ 前台功能

### 👤 用户系统
- ✅ 用户注册/登录/退出
- ✅ 个人中心管理
- ✅ 个人资料编辑
- ✅ 头像上传
- ✅ 密码修改
- ✅ 收货地址管理
- ✅ VIP会员系统

### 🛒 商品系统
- ✅ 商品展示（热门商品、新品上市）
- ✅ 商品详情页
- ✅ 商品分类浏览
- ✅ 商品搜索功能
- ✅ 商品排序（价格、销量、时间）
- ✅ 商品收藏功能
- ✅ 库存管理

### 🛍️ 购物车系统
- ✅ 添加商品到购物车
- ✅ 购物车商品管理
- ✅ 批量选择功能
- ✅ 实时价格计算
- ✅ 库存验证

### 📦 订单系统
- ✅ 订单创建
- ✅ 订单管理
- ✅ 订单状态跟踪
- ✅ 订单确认收货
- ✅ 优惠系统（满减）

### ⭐ 评价系统
- ✅ 商品评价
- ✅ 评分系统（5星）
- ✅ 评价图片上传
- ✅ 匿名评价
- ✅ 评价统计

### 🎨 界面特色
- ✅ 橙色主题设计
- ✅ 响应式布局
- ✅ 轮播图展示
- ✅ 多级导航
- ✅ 实时搜索

## 🔧 后台功能

### 👨‍💼 管理员系统
- ✅ 管理员注册/登录
- ✅ 权限管理
- ✅ 操作日志

### 📊 数据统计
- ✅ 仪表盘概览
- ✅ 用户统计
- ✅ 商品统计
- ✅ 订单统计
- ✅ 销售统计

### 👥 用户管理
- ✅ 用户列表
- ✅ 用户搜索
- ✅ 用户状态管理
- ✅ VIP管理

### 📦 商品管理
- ✅ 商品列表
- ✅ 商品添加/编辑/删除
- ✅ 商品状态管理
- ✅ 商品图片管理
- ✅ 库存管理
- ✅ 热门/新品标签

### 🏷️ 分类管理
- ✅ 分类列表
- ✅ 分类添加/编辑/删除
- ✅ 分类排序
- ✅ 分类图标管理

### 📋 订单管理
- ✅ 订单列表
- ✅ 订单搜索/筛选
- ✅ 订单详情
- ✅ 订单状态更新
- ✅ 发货管理

## 🛡️ 安全特性

### 🔐 权限控制
- ✅ 用户权限系统
- ✅ 管理员权限分级
- ✅ API权限控制
- ✅ CSRF保护

### 🔒 数据安全
- ✅ 密码加密
- ✅ SQL注入防护
- ✅ XSS防护
- ✅ 文件上传安全

## 📱 技术栈

### 🎨 前端技术
- **Bootstrap 5**: 响应式UI框架
- **Font Awesome**: 图标库
- **jQuery**: JavaScript库
- **AJAX**: 异步数据交互
- **CSS3**: 现代样式和动画

### 🔧 后端技术
- **Django 4.2**: Python Web框架
- **MySQL**: 关系型数据库
- **Django REST Framework**: API框架
- **Django Signals**: 信号机制
- **Pillow**: 图片处理

### 📊 数据管理
- **数据库索引**: 查询性能优化
- **分页显示**: 大数据量处理
- **缓存机制**: 数据缓存优化
- **文件管理**: 媒体文件统一管理

## 🎯 系统优势

### 🚀 性能优势
- ⚡ 快速响应
- 🔄 实时同步
- 📱 移动适配
- 🎨 美观界面

### 🛠️ 维护优势
- 🧹 代码规范
- 📝 详细日志
- 🔧 易于扩展
- 🛡️ 安全可靠

### 💼 商业价值
- 💰 完整电商流程
- 📈 数据分析
- 👥 用户管理
- 🎯 营销功能

## 📞 技术支持

如有问题，请联系：
- 📧 Email: <EMAIL>
- 📱 电话: 400-123-4567

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

---

© 2024 智能电商系统. All rights reserved.

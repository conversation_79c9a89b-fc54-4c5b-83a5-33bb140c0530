from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.urls import reverse


class Category(models.Model):
    """商品分类"""
    name = models.CharField(max_length=100, verbose_name='分类名称')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name='父分类')
    icon = models.ImageField(upload_to='category/', null=True, blank=True, verbose_name='分类图标')
    description = models.TextField(null=True, blank=True, verbose_name='分类描述')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    seo_title = models.CharField(max_length=200, null=True, blank=True, verbose_name='SEO标题')
    seo_keywords = models.Char<PERSON><PERSON>(max_length=200, null=True, blank=True, verbose_name='SEO关键词')
    seo_description = models.TextField(null=True, blank=True, verbose_name='SEO描述')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '商品分类'
        verbose_name_plural = '商品分类'
        ordering = ['sort_order', 'id']

    def __str__(self):
        if self.parent:
            return f"{self.parent.name} > {self.name}"
        return self.name

    @property
    def is_parent(self):
        """是否为父分类"""
        return self.parent is None

    @property
    def children(self):
        """子分类"""
        return self.category_set.filter(is_active=True)

    @property
    def product_count(self):
        """商品数量"""
        if self.is_parent:
            # 父分类统计所有子分类的商品
            return Product.objects.filter(category__parent=self, is_active=True).count()
        else:
            return self.product_set.filter(is_active=True).count()


class Brand(models.Model):
    """品牌"""
    name = models.CharField(max_length=100, verbose_name='品牌名称')
    logo = models.ImageField(upload_to='brands/', null=True, blank=True, verbose_name='品牌Logo')
    description = models.TextField(null=True, blank=True, verbose_name='品牌描述')
    website = models.URLField(null=True, blank=True, verbose_name='官方网站')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '品牌'
        verbose_name_plural = '品牌'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name


class Product(models.Model):
    """商品"""
    name = models.CharField(max_length=200, verbose_name='商品名称')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name='商品分类')
    brand = models.ForeignKey(Brand, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='品牌')
    sku = models.CharField(max_length=50, unique=True, verbose_name='商品编码')
    description = models.TextField(verbose_name='商品描述')
    detail = models.TextField(null=True, blank=True, verbose_name='详细描述')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='价格')
    original_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='原价')
    cost_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='成本价')
    main_image = models.ImageField(upload_to='products/', verbose_name='主图')
    
    # 库存相关
    stock = models.IntegerField(default=0, validators=[MinValueValidator(0)], verbose_name='库存')
    sales = models.IntegerField(default=0, validators=[MinValueValidator(0)], verbose_name='销量')
    virtual_sales = models.IntegerField(default=0, validators=[MinValueValidator(0)], verbose_name='虚拟销量')
    
    # 商品属性
    weight = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, verbose_name='重量(kg)')
    length = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, verbose_name='长度(cm)')
    width = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, verbose_name='宽度(cm)')
    height = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, verbose_name='高度(cm)')
    
    # 状态标签
    is_hot = models.BooleanField(default=False, verbose_name='热门商品')
    is_new = models.BooleanField(default=False, verbose_name='新品')
    is_recommend = models.BooleanField(default=False, verbose_name='推荐商品')
    is_active = models.BooleanField(default=True, verbose_name='是否上架')
    
    # SEO相关
    seo_title = models.CharField(max_length=200, null=True, blank=True, verbose_name='SEO标题')
    seo_keywords = models.CharField(max_length=200, null=True, blank=True, verbose_name='SEO关键词')
    seo_description = models.TextField(null=True, blank=True, verbose_name='SEO描述')
    
    # 时间字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '商品'
        verbose_name_plural = '商品'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['category', 'is_active']),
            models.Index(fields=['is_hot', 'is_active']),
            models.Index(fields=['is_new', 'is_active']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('products:detail', kwargs={'product_id': self.id})

    @property
    def total_sales(self):
        """总销量（实际销量 + 虚拟销量）"""
        return self.sales + self.virtual_sales

    @property
    def average_rating(self):
        """平均评分"""
        try:
            from reviews.models import Review
            reviews = Review.objects.filter(product=self)
            if reviews.exists():
                return reviews.aggregate(avg_rating=models.Avg('rating'))['avg_rating']
        except ImportError:
            pass
        return 0

    @property
    def review_count(self):
        """评价数量"""
        try:
            from reviews.models import Review
            return Review.objects.filter(product=self).count()
        except ImportError:
            pass
        return 0

    @property
    def is_in_stock(self):
        """是否有库存"""
        return self.stock > 0

    @property
    def stock_status(self):
        """库存状态"""
        if self.stock <= 0:
            return 'out_of_stock'
        elif self.stock <= 10:
            return 'low_stock'
        else:
            return 'in_stock'

    @property
    def discount_percentage(self):
        """折扣百分比"""
        if self.original_price and self.original_price > self.price:
            return int((1 - self.price / self.original_price) * 100)
        return 0

    def reduce_stock(self, quantity):
        """减少库存"""
        if self.stock >= quantity:
            self.stock -= quantity
            self.sales += quantity
            self.save()
            return True
        return False

    def increase_stock(self, quantity):
        """增加库存"""
        self.stock += quantity
        self.save()


class ProductImage(models.Model):
    """商品图片"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name='商品')
    image = models.ImageField(upload_to='products/', verbose_name='图片')
    alt_text = models.CharField(max_length=200, null=True, blank=True, verbose_name='图片描述')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '商品图片'
        verbose_name_plural = '商品图片'
        ordering = ['sort_order', 'id']

    def __str__(self):
        return f"{self.product.name} - 图片{self.id}"


class ProductAttribute(models.Model):
    """商品属性"""
    name = models.CharField(max_length=50, verbose_name='属性名称')
    is_required = models.BooleanField(default=False, verbose_name='是否必填')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '商品属性'
        verbose_name_plural = '商品属性'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name


class ProductAttributeValue(models.Model):
    """商品属性值"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name='商品')
    attribute = models.ForeignKey(ProductAttribute, on_delete=models.CASCADE, verbose_name='属性')
    value = models.CharField(max_length=200, verbose_name='属性值')

    class Meta:
        verbose_name = '商品属性值'
        verbose_name_plural = '商品属性值'
        unique_together = ['product', 'attribute']

    def __str__(self):
        return f"{self.product.name} - {self.attribute.name}: {self.value}"


class Favorite(models.Model):
    """收藏"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name='商品')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='收藏时间')

    class Meta:
        verbose_name = '收藏'
        verbose_name_plural = '收藏'
        unique_together = ['user', 'product']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.product.name}"


class Banner(models.Model):
    """轮播图"""
    title = models.CharField(max_length=100, verbose_name='标题')
    subtitle = models.CharField(max_length=200, null=True, blank=True, verbose_name='副标题')
    image = models.ImageField(upload_to='banners/', verbose_name='图片')
    link = models.URLField(null=True, blank=True, verbose_name='链接')
    target_blank = models.BooleanField(default=False, verbose_name='新窗口打开')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    start_time = models.DateTimeField(null=True, blank=True, verbose_name='开始时间')
    end_time = models.DateTimeField(null=True, blank=True, verbose_name='结束时间')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '轮播图'
        verbose_name_plural = '轮播图'
        ordering = ['sort_order', '-created_at']

    def __str__(self):
        return self.title

{% extends 'shop/base.html' %}

{% block title %}{{ product.name }} - 智能电商{% endblock %}

{% block content %}
<div class="container my-4">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'shop:index' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'shop:product_list' %}">商品</a></li>
            <li class="breadcrumb-item"><a href="{% url 'shop:product_list' %}?category={{ product.category.id }}">{{ product.category.name }}</a></li>
            <li class="breadcrumb-item active">{{ product.name }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- 商品图片 -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <div id="productCarousel" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-inner">
                            <div class="carousel-item active">
                                <img src="{{ product.main_image.url }}" class="d-block w-100" alt="{{ product.name }}" style="height: 400px; object-fit: cover;">
                            </div>
                            {% for image in product_images %}
                            <div class="carousel-item">
                                <img src="{{ image.image.url }}" class="d-block w-100" alt="{{ product.name }}" style="height: 400px; object-fit: cover;">
                            </div>
                            {% endfor %}
                        </div>
                        {% if product_images %}
                        <button class="carousel-control-prev" type="button" data-bs-target="#productCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon"></span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#productCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon"></span>
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 商品信息 -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h2>{{ product.name }}</h2>
                    
                    <!-- 标签 -->
                    <div class="mb-3">
                        {% if product.is_hot %}
                        <span class="badge bg-danger me-2">热门</span>
                        {% endif %}
                        {% if product.is_new %}
                        <span class="badge bg-success me-2">新品</span>
                        {% endif %}
                    </div>

                    <!-- 价格 -->
                    <div class="mb-3">
                        <span class="h3 text-primary">¥{{ product.price }}</span>
                        {% if product.original_price %}
                        <span class="h5 text-muted text-decoration-line-through ms-2">¥{{ product.original_price }}</span>
                        {% endif %}
                    </div>

                    <!-- 评分和销量 -->
                    <div class="mb-3">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                {% for i in "12345" %}
                                {% if forloop.counter <= product.average_rating %}
                                <i class="fas fa-star text-warning"></i>
                                {% else %}
                                <i class="far fa-star text-warning"></i>
                                {% endif %}
                                {% endfor %}
                                <span class="ms-2">{{ product.average_rating|floatformat:1 }}</span>
                            </div>
                            <div class="text-muted">
                                <span>{{ product.review_count }} 条评价</span>
                                <span class="mx-2">|</span>
                                <span>销量 {{ product.sales }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 库存 -->
                    <div class="mb-3">
                        <span class="text-muted">库存：</span>
                        {% if product.stock > 0 %}
                        <span class="text-success">{{ product.stock }} 件</span>
                        {% else %}
                        <span class="text-danger">缺货</span>
                        {% endif %}
                    </div>

                    <!-- 商品描述 -->
                    <div class="mb-4">
                        <h6>商品描述</h6>
                        <p class="text-muted">{{ product.description }}</p>
                    </div>

                    <!-- 操作按钮 -->
                    {% if user.is_authenticated %}
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">数量</label>
                            <input type="number" class="form-control" id="quantity" value="1" min="1" max="{{ product.stock }}">
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2 mb-3">
                        {% if product.stock > 0 %}
                        <button class="btn btn-primary flex-fill" id="addToCart">
                            <i class="fas fa-cart-plus me-2"></i>加入购物车
                        </button>
                        <button class="btn btn-success flex-fill" id="buyNow">
                            <i class="fas fa-bolt me-2"></i>立即购买
                        </button>
                        {% else %}
                        <button class="btn btn-secondary flex-fill" disabled>
                            <i class="fas fa-times me-2"></i>暂时缺货
                        </button>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" id="favoriteBtn" data-favorited="{{ is_favorited|yesno:'true,false' }}">
                            {% if is_favorited %}
                            <i class="fas fa-heart text-danger me-2"></i>已收藏
                            {% else %}
                            <i class="far fa-heart me-2"></i>收藏
                            {% endif %}
                        </button>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        请 <a href="{% url 'shop:login' %}">登录</a> 后购买商品
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 商品详情和评价 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#details">商品详情</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#reviews">用户评价 ({{ product.review_count }})</a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="details">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>基本信息</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td>商品名称：</td>
                                            <td>{{ product.name }}</td>
                                        </tr>
                                        <tr>
                                            <td>商品分类：</td>
                                            <td>{{ product.category.name }}</td>
                                        </tr>
                                        <tr>
                                            <td>商品价格：</td>
                                            <td class="text-primary">¥{{ product.price }}</td>
                                        </tr>
                                        <tr>
                                            <td>上架时间：</td>
                                            <td>{{ product.created_at|date:"Y-m-d" }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6>商品描述</h6>
                                    <p>{{ product.description }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="reviews">
                            {% for review in reviews %}
                            <div class="border-bottom pb-3 mb-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong>
                                            {% if review.is_anonymous %}
                                            匿名用户
                                            {% else %}
                                            {{ review.user.username }}
                                            {% endif %}
                                        </strong>
                                        <div class="text-warning">
                                            {% for i in "12345" %}
                                            {% if forloop.counter <= review.rating %}
                                            <i class="fas fa-star"></i>
                                            {% else %}
                                            <i class="far fa-star"></i>
                                            {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <small class="text-muted">{{ review.created_at|date:"Y-m-d H:i" }}</small>
                                </div>
                                <p class="mt-2">{{ review.content }}</p>
                            </div>
                            {% empty %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-comment-slash fa-2x mb-2"></i>
                                <p>暂无评价</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 相关商品 -->
    {% if related_products %}
    <div class="row mt-4">
        <div class="col-12">
            <h4>相关商品</h4>
            <div class="row">
                {% for product in related_products %}
                <div class="col-md-3 mb-3">
                    <div class="card product-card h-100">
                        <img src="{{ product.main_image.url }}" class="card-img-top" alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                        <div class="card-body">
                            <h6 class="card-title">{{ product.name }}</h6>
                            <p class="text-primary">¥{{ product.price }}</p>
                            <a href="{% url 'shop:product_detail' product.id %}" class="btn btn-outline-primary btn-sm">查看详情</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 添加到购物车
    $('#addToCart').click(function() {
        var quantity = $('#quantity').val();
        
        $.ajax({
            url: '{% url "shop:add_to_cart" %}',
            type: 'POST',
            data: JSON.stringify({
                'product_id': {{ product.id }},
                'quantity': parseInt(quantity)
            }),
            contentType: 'application/json',
            headers: {
                'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.success) {
                    alert('添加成功！');
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('添加失败，请重试');
            }
        });
    });

    // 立即购买
    $('#buyNow').click(function() {
        $('#addToCart').click();
        setTimeout(function() {
            window.location.href = '{% url "shop:cart" %}';
        }, 1000);
    });

    // 收藏功能
    $('#favoriteBtn').click(function() {
        var button = $(this);
        var favorited = button.data('favorited');
        
        // 这里可以添加收藏/取消收藏的AJAX请求
        if (favorited) {
            button.html('<i class="far fa-heart me-2"></i>收藏');
            button.data('favorited', false);
        } else {
            button.html('<i class="fas fa-heart text-danger me-2"></i>已收藏');
            button.data('favorited', true);
        }
    });
});
</script>
{% endblock %}

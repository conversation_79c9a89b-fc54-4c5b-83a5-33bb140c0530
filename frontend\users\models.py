from django.db import models
from django.contrib.auth.models import User
from django.core.validators import RegexValidator


class VIPLevel(models.Model):
    """VIP等级"""
    name = models.CharField(max_length=50, verbose_name='等级名称')
    level = models.IntegerField(unique=True, verbose_name='等级数值')
    min_consumption = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='最低消费金额')
    discount_rate = models.DecimalField(max_digits=5, decimal_places=4, default=1.0000, verbose_name='折扣率')
    benefits = models.TextField(verbose_name='会员权益')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = 'VIP等级'
        verbose_name_plural = 'VIP等级'
        ordering = ['level']

    def __str__(self):
        return f"{self.name} (等级{self.level})"


class UserProfile(models.Model):
    """用户资料扩展"""
    GENDER_CHOICES = [
        ('male', '男'),
        ('female', '女'),
        ('other', '其他'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    avatar = models.ImageField(upload_to='avatars/', null=True, blank=True, verbose_name='头像')
    phone_validator = RegexValidator(
        regex=r'^1[3-9]\d{9}$',
        message='请输入有效的手机号码'
    )
    phone = models.CharField(
        max_length=11, 
        validators=[phone_validator], 
        null=True, 
        blank=True, 
        verbose_name='手机号'
    )
    gender = models.CharField(
        max_length=10, 
        choices=GENDER_CHOICES, 
        null=True, 
        blank=True, 
        verbose_name='性别'
    )
    birthday = models.DateField(null=True, blank=True, verbose_name='生日')
    vip_level = models.ForeignKey(
        VIPLevel, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        verbose_name='VIP等级'
    )
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='账户余额')
    total_consumption = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='累计消费')
    points = models.IntegerField(default=0, verbose_name='积分')
    is_email_verified = models.BooleanField(default=False, verbose_name='邮箱已验证')
    is_phone_verified = models.BooleanField(default=False, verbose_name='手机已验证')
    last_login_ip = models.GenericIPAddressField(null=True, blank=True, verbose_name='最后登录IP')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'

    def __str__(self):
        return f"{self.user.username}的资料"

    @property
    def is_vip(self):
        """是否为VIP用户"""
        return self.vip_level is not None

    @property
    def vip_name(self):
        """VIP等级名称"""
        return self.vip_level.name if self.vip_level else '普通会员'

    def update_vip_level(self):
        """根据累计消费更新VIP等级"""
        suitable_level = VIPLevel.objects.filter(
            min_consumption__lte=self.total_consumption
        ).order_by('-level').first()
        
        if suitable_level and suitable_level != self.vip_level:
            self.vip_level = suitable_level
            self.save()
            return True
        return False

    def add_points(self, amount):
        """添加积分"""
        self.points += amount
        self.save()

    def consume_points(self, amount):
        """消费积分"""
        if self.points >= amount:
            self.points -= amount
            self.save()
            return True
        return False


class Address(models.Model):
    """收货地址"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    name = models.CharField(max_length=50, verbose_name='收货人')
    phone_validator = RegexValidator(
        regex=r'^1[3-9]\d{9}$',
        message='请输入有效的手机号码'
    )
    phone = models.CharField(max_length=11, validators=[phone_validator], verbose_name='手机号')
    province = models.CharField(max_length=50, verbose_name='省份')
    city = models.CharField(max_length=50, verbose_name='城市')
    district = models.CharField(max_length=50, verbose_name='区县')
    address = models.CharField(max_length=200, verbose_name='详细地址')
    postal_code = models.CharField(max_length=6, null=True, blank=True, verbose_name='邮政编码')
    is_default = models.BooleanField(default=False, verbose_name='默认地址')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '收货地址'
        verbose_name_plural = '收货地址'
        ordering = ['-is_default', '-updated_at']

    def __str__(self):
        return f"{self.name} - {self.province}{self.city}{self.district}{self.address}"

    def save(self, *args, **kwargs):
        # 如果设置为默认地址，取消其他默认地址
        if self.is_default:
            Address.objects.filter(user=self.user, is_default=True).update(is_default=False)
        super().save(*args, **kwargs)

    @property
    def full_address(self):
        """完整地址"""
        return f"{self.province}{self.city}{self.district}{self.address}"


class UserLoginLog(models.Model):
    """用户登录日志"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    ip_address = models.GenericIPAddressField(verbose_name='IP地址')
    user_agent = models.TextField(verbose_name='用户代理')
    login_time = models.DateTimeField(auto_now_add=True, verbose_name='登录时间')
    is_success = models.BooleanField(default=True, verbose_name='登录成功')
    failure_reason = models.CharField(max_length=100, null=True, blank=True, verbose_name='失败原因')

    class Meta:
        verbose_name = '登录日志'
        verbose_name_plural = '登录日志'
        ordering = ['-login_time']

    def __str__(self):
        return f"{self.user.username} - {self.login_time.strftime('%Y-%m-%d %H:%M:%S')}"


class UserBalanceLog(models.Model):
    """用户余额变动日志"""
    OPERATION_CHOICES = [
        ('recharge', '充值'),
        ('consume', '消费'),
        ('refund', '退款'),
        ('reward', '奖励'),
        ('deduct', '扣除'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    operation_type = models.CharField(max_length=20, choices=OPERATION_CHOICES, verbose_name='操作类型')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='变动金额')
    balance_before = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='变动前余额')
    balance_after = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='变动后余额')
    description = models.CharField(max_length=200, verbose_name='变动说明')
    related_order = models.CharField(max_length=50, null=True, blank=True, verbose_name='关联订单号')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '余额变动日志'
        verbose_name_plural = '余额变动日志'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.get_operation_type_display()} - {self.amount}"

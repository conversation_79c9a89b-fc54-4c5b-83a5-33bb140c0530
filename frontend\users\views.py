from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.core.paginator import Paginator
import json

from .models import UserProfile, Address, VIPLevel, UserLoginLog, UserBalanceLog


def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def user_register(request):
    """用户注册"""
    if request.method == 'POST':
        username = request.POST.get('username')
        email = request.POST.get('email')
        password = request.POST.get('password')
        confirm_password = request.POST.get('confirm_password')
        phone = request.POST.get('phone')
        
        # 验证
        if not all([username, email, password, confirm_password]):
            messages.error(request, '请填写完整信息')
            return render(request, 'users/register.html')
        
        if password != confirm_password:
            messages.error(request, '两次密码不一致')
            return render(request, 'users/register.html')
        
        if len(password) < 6:
            messages.error(request, '密码长度至少6位')
            return render(request, 'users/register.html')
        
        if User.objects.filter(username=username).exists():
            messages.error(request, '用户名已存在')
            return render(request, 'users/register.html')
        
        if User.objects.filter(email=email).exists():
            messages.error(request, '邮箱已被注册')
            return render(request, 'users/register.html')
        
        # 创建用户
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password
        )
        
        # 创建用户资料
        UserProfile.objects.create(
            user=user,
            phone=phone if phone else None
        )
        
        # 记录注册日志
        UserLoginLog.objects.create(
            user=user,
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            is_success=True
        )
        
        messages.success(request, '注册成功，请登录')
        return redirect('users:login')
    
    return render(request, 'users/register.html')


def user_login(request):
    """用户登录"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        
        user = authenticate(request, username=username, password=password)
        if user:
            login(request, user)
            
            # 更新用户资料中的最后登录IP
            profile, created = UserProfile.objects.get_or_create(user=user)
            profile.last_login_ip = get_client_ip(request)
            profile.save()
            
            # 记录登录日志
            UserLoginLog.objects.create(
                user=user,
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                is_success=True
            )
            
            next_url = request.GET.get('next', 'products:index')
            return redirect(next_url)
        else:
            # 记录登录失败日志
            try:
                failed_user = User.objects.get(username=username)
                UserLoginLog.objects.create(
                    user=failed_user,
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    is_success=False,
                    failure_reason='密码错误'
                )
            except User.DoesNotExist:
                pass
            
            messages.error(request, '用户名或密码错误')
    
    return render(request, 'users/login.html')


def user_logout(request):
    """用户退出"""
    logout(request)
    return redirect('products:index')


@login_required
def user_profile(request):
    """个人中心"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)
    
    if request.method == 'POST':
        # 更新用户信息
        request.user.first_name = request.POST.get('first_name', '')
        request.user.last_name = request.POST.get('last_name', '')
        request.user.email = request.POST.get('email', '')
        request.user.save()
        
        # 更新用户资料
        profile.phone = request.POST.get('phone', '')
        profile.gender = request.POST.get('gender', '')
        if request.POST.get('birthday'):
            profile.birthday = request.POST.get('birthday')
        
        if 'avatar' in request.FILES:
            profile.avatar = request.FILES['avatar']
        
        profile.save()
        messages.success(request, '资料更新成功')
    
    context = {
        'profile': profile,
        'vip_levels': VIPLevel.objects.all(),
    }
    return render(request, 'users/profile.html', context)


@login_required
def address_list(request):
    """收货地址列表"""
    addresses = Address.objects.filter(user=request.user)
    
    context = {
        'addresses': addresses,
    }
    return render(request, 'users/address_list.html', context)


@login_required
def address_add(request):
    """添加收货地址"""
    if request.method == 'POST':
        name = request.POST.get('name')
        phone = request.POST.get('phone')
        province = request.POST.get('province')
        city = request.POST.get('city')
        district = request.POST.get('district')
        address = request.POST.get('address')
        postal_code = request.POST.get('postal_code')
        is_default = request.POST.get('is_default') == 'on'
        
        # 验证
        if not all([name, phone, province, city, district, address]):
            messages.error(request, '请填写完整信息')
            return render(request, 'users/address_form.html')
        
        # 创建地址
        Address.objects.create(
            user=request.user,
            name=name,
            phone=phone,
            province=province,
            city=city,
            district=district,
            address=address,
            postal_code=postal_code,
            is_default=is_default
        )
        
        messages.success(request, '地址添加成功')
        return redirect('users:address_list')
    
    return render(request, 'users/address_form.html')


@login_required
def address_edit(request, address_id):
    """编辑收货地址"""
    address = get_object_or_404(Address, id=address_id, user=request.user)
    
    if request.method == 'POST':
        address.name = request.POST.get('name')
        address.phone = request.POST.get('phone')
        address.province = request.POST.get('province')
        address.city = request.POST.get('city')
        address.district = request.POST.get('district')
        address.address = request.POST.get('address')
        address.postal_code = request.POST.get('postal_code')
        address.is_default = request.POST.get('is_default') == 'on'
        
        address.save()
        messages.success(request, '地址更新成功')
        return redirect('users:address_list')
    
    context = {
        'address': address,
        'is_edit': True,
    }
    return render(request, 'users/address_form.html', context)


@login_required
def address_delete(request, address_id):
    """删除收货地址"""
    address = get_object_or_404(Address, id=address_id, user=request.user)
    address.delete()
    messages.success(request, '地址删除成功')
    return redirect('users:address_list')


@login_required
@csrf_exempt
def set_default_address(request):
    """设置默认地址"""
    if request.method == 'POST':
        data = json.loads(request.body)
        address_id = data.get('address_id')
        
        try:
            # 取消所有默认地址
            Address.objects.filter(user=request.user).update(is_default=False)
            
            # 设置新的默认地址
            address = Address.objects.get(id=address_id, user=request.user)
            address.is_default = True
            address.save()
            
            return JsonResponse({'success': True, 'message': '默认地址设置成功'})
        except Address.DoesNotExist:
            return JsonResponse({'success': False, 'message': '地址不存在'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
def balance_log(request):
    """余额变动记录"""
    logs = UserBalanceLog.objects.filter(user=request.user)
    
    # 分页
    paginator = Paginator(logs, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
    }
    return render(request, 'users/balance_log.html', context)


@login_required
def vip_center(request):
    """VIP中心"""
    profile = get_object_or_404(UserProfile, user=request.user)
    vip_levels = VIPLevel.objects.all()
    
    # 计算升级到下一等级需要的消费金额
    next_level = None
    if profile.vip_level:
        next_level = VIPLevel.objects.filter(level__gt=profile.vip_level.level).first()
    else:
        next_level = VIPLevel.objects.first()
    
    upgrade_needed = 0
    if next_level:
        upgrade_needed = max(0, next_level.min_consumption - profile.total_consumption)
    
    context = {
        'profile': profile,
        'vip_levels': vip_levels,
        'next_level': next_level,
        'upgrade_needed': upgrade_needed,
    }
    return render(request, 'users/vip_center.html', context)

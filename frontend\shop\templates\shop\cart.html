{% extends 'shop/base.html' %}

{% block title %}购物车 - 智能电商{% endblock %}

{% block content %}
<div class="container my-4">
    <h2><i class="fas fa-shopping-cart me-2"></i>购物车</h2>
    
    {% if cart_items %}
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-1">
                            <input type="checkbox" id="selectAll" class="form-check-input">
                        </div>
                        <div class="col-5">商品信息</div>
                        <div class="col-2 text-center">单价</div>
                        <div class="col-2 text-center">数量</div>
                        <div class="col-2 text-center">操作</div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% for item in cart_items %}
                    <div class="cart-item border-bottom p-3" data-item-id="{{ item.id }}" data-product-id="{{ item.product.id }}">
                        <div class="row align-items-center">
                            <div class="col-1">
                                <input type="checkbox" class="form-check-input item-checkbox" data-price="{{ item.total_price }}">
                            </div>
                            <div class="col-5">
                                <div class="d-flex align-items-center">
                                    <img src="{{ item.product.main_image.url }}" alt="{{ item.product.name }}" 
                                         class="rounded me-3" style="width: 80px; height: 80px; object-fit: cover;">
                                    <div>
                                        <h6 class="mb-1">{{ item.product.name }}</h6>
                                        <small class="text-muted">{{ item.product.category.name }}</small>
                                        {% if item.product.stock < item.quantity %}
                                        <div class="text-danger small">库存不足</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-2 text-center">
                                <span class="text-primary">¥{{ item.product.price }}</span>
                            </div>
                            <div class="col-2 text-center">
                                <div class="input-group input-group-sm" style="width: 120px;">
                                    <button class="btn btn-outline-secondary quantity-btn" type="button" data-action="decrease">-</button>
                                    <input type="number" class="form-control text-center quantity-input" 
                                           value="{{ item.quantity }}" min="1" max="{{ item.product.stock }}">
                                    <button class="btn btn-outline-secondary quantity-btn" type="button" data-action="increase">+</button>
                                </div>
                            </div>
                            <div class="col-2 text-center">
                                <button class="btn btn-outline-danger btn-sm remove-item" data-item-id="{{ item.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-6 offset-6 text-end">
                                <span class="text-muted">小计：</span>
                                <span class="text-primary fw-bold item-total">¥{{ item.total_price }}</span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>订单结算</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>商品总价：</span>
                        <span id="subtotal">¥0.00</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>运费：</span>
                        <span class="text-success">免费</span>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>优惠：</span>
                        <span id="discount" class="text-success">-¥0.00</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-3">
                        <strong>应付总额：</strong>
                        <strong class="text-primary h5" id="total">¥0.00</strong>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-gift me-1"></i>
                            满100减10，满300减50，满1000减200
                        </small>
                    </div>
                    
                    <button class="btn btn-primary w-100" id="checkoutBtn" disabled>
                        <i class="fas fa-credit-card me-2"></i>去结算
                    </button>
                </div>
            </div>
            
            <!-- 推荐商品 -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6>为您推荐</h6>
                </div>
                <div class="card-body">
                    <div class="text-center text-muted">
                        <i class="fas fa-heart"></i>
                        <p class="small">根据您的购物车推荐相关商品</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
        <h4 class="text-muted">购物车是空的</h4>
        <p class="text-muted">快去挑选喜欢的商品吧！</p>
        <a href="{% url 'shop:product_list' %}" class="btn btn-primary">
            <i class="fas fa-shopping-bag me-2"></i>去购物
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 全选/取消全选
    $('#selectAll').change(function() {
        $('.item-checkbox').prop('checked', $(this).prop('checked'));
        updateTotal();
    });

    // 单个商品选择
    $('.item-checkbox').change(function() {
        updateSelectAll();
        updateTotal();
    });

    // 数量调整
    $('.quantity-btn').click(function() {
        var action = $(this).data('action');
        var input = $(this).siblings('.quantity-input');
        var currentValue = parseInt(input.val());
        var max = parseInt(input.attr('max'));
        
        if (action === 'increase' && currentValue < max) {
            input.val(currentValue + 1);
        } else if (action === 'decrease' && currentValue > 1) {
            input.val(currentValue - 1);
        }
        
        updateItemTotal($(this).closest('.cart-item'));
        updateTotal();
    });

    // 数量输入框变化
    $('.quantity-input').change(function() {
        var value = parseInt($(this).val());
        var max = parseInt($(this).attr('max'));
        
        if (value < 1) {
            $(this).val(1);
        } else if (value > max) {
            $(this).val(max);
            alert('库存不足，最多只能购买 ' + max + ' 件');
        }
        
        updateItemTotal($(this).closest('.cart-item'));
        updateTotal();
    });

    // 删除商品
    $('.remove-item').click(function() {
        if (confirm('确定要删除这件商品吗？')) {
            var itemId = $(this).data('item-id');
            var cartItem = $(this).closest('.cart-item');
            
            // 这里可以添加AJAX删除请求
            cartItem.fadeOut(300, function() {
                $(this).remove();
                updateTotal();
                updateSelectAll();
            });
        }
    });

    // 结算按钮
    $('#checkoutBtn').click(function() {
        var selectedItems = $('.item-checkbox:checked');
        if (selectedItems.length === 0) {
            alert('请选择要结算的商品');
            return;
        }
        
        // 跳转到结算页面
        window.location.href = '{% url "shop:create_order" %}';
    });

    // 更新单个商品小计
    function updateItemTotal(cartItem) {
        var price = parseFloat(cartItem.find('.text-primary').text().replace('¥', ''));
        var quantity = parseInt(cartItem.find('.quantity-input').val());
        var total = price * quantity;
        
        cartItem.find('.item-total').text('¥' + total.toFixed(2));
        cartItem.find('.item-checkbox').data('price', total);
    }

    // 更新总计
    function updateTotal() {
        var subtotal = 0;
        $('.item-checkbox:checked').each(function() {
            subtotal += parseFloat($(this).data('price'));
        });
        
        // 计算优惠
        var discount = 0;
        if (subtotal >= 1000) {
            discount = 200;
        } else if (subtotal >= 300) {
            discount = 50;
        } else if (subtotal >= 100) {
            discount = 10;
        }
        
        var total = subtotal - discount;
        
        $('#subtotal').text('¥' + subtotal.toFixed(2));
        $('#discount').text('-¥' + discount.toFixed(2));
        $('#total').text('¥' + total.toFixed(2));
        
        // 启用/禁用结算按钮
        $('#checkoutBtn').prop('disabled', subtotal === 0);
    }

    // 更新全选状态
    function updateSelectAll() {
        var totalItems = $('.item-checkbox').length;
        var checkedItems = $('.item-checkbox:checked').length;
        $('#selectAll').prop('checked', totalItems > 0 && totalItems === checkedItems);
    }

    // 初始化
    updateTotal();
});
</script>
{% endblock %}

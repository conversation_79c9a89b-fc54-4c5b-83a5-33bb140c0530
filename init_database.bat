@echo off
echo 初始化智能电商数据库...
echo.

echo 1. 创建前台数据库迁移文件...
cd frontend
python manage.py makemigrations shop
if %errorlevel% neq 0 (
    echo 前台迁移文件创建失败！
    pause
    exit /b 1
)

echo 2. 执行前台数据库迁移...
python manage.py migrate
if %errorlevel% neq 0 (
    echo 前台数据库迁移失败！
    pause
    exit /b 1
)

echo 3. 创建后台数据库迁移文件...
cd ..\backend
python manage.py makemigrations management
if %errorlevel% neq 0 (
    echo 后台迁移文件创建失败！
    pause
    exit /b 1
)

echo 4. 执行后台数据库迁移...
python manage.py migrate
if %errorlevel% neq 0 (
    echo 后台数据库迁移失败！
    pause
    exit /b 1
)

echo.
echo 数据库初始化完成！
echo.
echo 注意：请确保MySQL服务已启动，并且已创建名为'shopping'的数据库
echo.
echo 按任意键继续...
pause >nul

cd ..

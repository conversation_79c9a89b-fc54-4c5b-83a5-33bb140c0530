{% extends 'management/base.html' %}

{% block title %}添加商品 - 智能电商管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-plus me-2"></i>添加商品</h1>
    <a href="{% url 'management:product_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>返回列表
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>商品信息</h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label class="form-label">商品名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">商品分类 <span class="text-danger">*</span></label>
                            <select class="form-select" name="category" required>
                                <option value="">请选择分类</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">商品描述 <span class="text-danger">*</span></label>
                        <textarea class="form-control" name="description" rows="4" required></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">销售价格 <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">¥</span>
                                <input type="number" class="form-control" name="price" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">原价</label>
                            <div class="input-group">
                                <span class="input-group-text">¥</span>
                                <input type="number" class="form-control" name="original_price" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">库存数量 <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" name="stock" min="0" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">商品主图 <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" name="main_image" accept="image/*" required>
                        <div class="form-text">支持 JPG、PNG、GIF 格式，建议尺寸 800x800 像素</div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_hot" id="is_hot">
                                <label class="form-check-label" for="is_hot">
                                    <i class="fas fa-fire text-danger me-1"></i>热门商品
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_new" id="is_new">
                                <label class="form-check-label" for="is_new">
                                    <i class="fas fa-star text-success me-1"></i>新品上市
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    <i class="fas fa-eye text-primary me-1"></i>立即上架
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>保存商品
                        </button>
                        <button type="reset" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-2"></i>重置
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- 商品预览 -->
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-eye me-2"></i>商品预览</h6>
            </div>
            <div class="card-body">
                <div id="product-preview">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-image fa-3x mb-2"></i>
                        <p>选择商品图片后显示预览</p>
                    </div>
                </div>
                
                <div id="preview-info" style="display: none;">
                    <h6 id="preview-name" class="mt-3">商品名称</h6>
                    <p id="preview-description" class="text-muted small">商品描述</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span id="preview-price" class="h6 text-primary">¥0.00</span>
                        <span id="preview-stock" class="text-muted small">库存: 0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作提示 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-lightbulb me-2"></i>操作提示</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>商品名称要简洁明了</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>商品描述要详细准确</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>图片要清晰美观</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>价格要合理设置</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>库存要及时更新</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 图片预览
    $('input[name="main_image"]').change(function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#product-preview').html(
                    '<img src="' + e.target.result + '" class="img-fluid rounded" alt="商品预览">'
                );
                updatePreview();
            };
            reader.readAsDataURL(file);
        }
    });

    // 实时预览更新
    $('input[name="name"], textarea[name="description"], input[name="price"], input[name="stock"]').on('input', function() {
        updatePreview();
    });

    function updatePreview() {
        var name = $('input[name="name"]').val() || '商品名称';
        var description = $('textarea[name="description"]').val() || '商品描述';
        var price = $('input[name="price"]').val() || '0.00';
        var stock = $('input[name="stock"]').val() || '0';

        $('#preview-name').text(name);
        $('#preview-description').text(description.substring(0, 50) + (description.length > 50 ? '...' : ''));
        $('#preview-price').text('¥' + price);
        $('#preview-stock').text('库存: ' + stock);
        
        if (name || description || price !== '0.00' || stock !== '0') {
            $('#preview-info').show();
        }
    }

    // 表单验证
    $('form').submit(function(e) {
        var price = parseFloat($('input[name="price"]').val());
        var originalPrice = parseFloat($('input[name="original_price"]').val());
        
        if (originalPrice && originalPrice <= price) {
            alert('原价应该大于销售价格');
            e.preventDefault();
            return false;
        }
        
        var stock = parseInt($('input[name="stock"]').val());
        if (stock < 0) {
            alert('库存数量不能为负数');
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}

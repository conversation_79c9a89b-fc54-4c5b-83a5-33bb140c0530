{% extends 'base.html' %}

{% block title %}我的订单 - 智能电商{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">我的订单</h1>
    </div>

    <!-- 订单状态筛选 -->
    <div class="bg-white rounded-xl shadow-sm mb-6">
        <div class="flex overflow-x-auto">
            <button class="flex-shrink-0 px-6 py-4 text-orange-primary border-b-2 border-orange-primary font-medium">
                全部订单
            </button>
            <button class="flex-shrink-0 px-6 py-4 text-gray-600 hover:text-orange-primary transition-colors">
                待付款 <span class="ml-1 bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">2</span>
            </button>
            <button class="flex-shrink-0 px-6 py-4 text-gray-600 hover:text-orange-primary transition-colors">
                待发货 <span class="ml-1 bg-yellow-100 text-yellow-600 text-xs px-2 py-1 rounded-full">1</span>
            </button>
            <button class="flex-shrink-0 px-6 py-4 text-gray-600 hover:text-orange-primary transition-colors">
                待收货 <span class="ml-1 bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">3</span>
            </button>
            <button class="flex-shrink-0 px-6 py-4 text-gray-600 hover:text-orange-primary transition-colors">
                待评价 <span class="ml-1 bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">1</span>
            </button>
            <button class="flex-shrink-0 px-6 py-4 text-gray-600 hover:text-orange-primary transition-colors">
                已完成
            </button>
            <button class="flex-shrink-0 px-6 py-4 text-gray-600 hover:text-orange-primary transition-colors">
                已取消
            </button>
        </div>
    </div>

    <!-- 订单列表 -->
    <div class="space-y-4">
        <!-- 订单1 -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <!-- 订单头部 -->
            <div class="bg-gray-50 px-6 py-4 border-b">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">订单号：ORD202401001</span>
                        <span class="text-sm text-gray-600">下单时间：2024-01-15 10:30</span>
                    </div>
                    <span class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full">已完成</span>
                </div>
            </div>

            <!-- 订单商品 -->
            <div class="p-6">
                <div class="space-y-4">
                    <!-- 商品1 -->
                    <div class="flex items-center space-x-4">
                        <img src="https://picsum.photos/80/80?random=10" alt="智能手表" class="w-20 h-20 object-cover rounded-lg">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-800">智能手表 Pro</h3>
                            <p class="text-sm text-gray-500 mt-1">颜色：黑色 | 尺寸：42mm</p>
                            <div class="flex items-center mt-2">
                                <span class="text-orange-primary font-bold">¥1,299</span>
                                <span class="text-gray-400 text-sm ml-2">x1</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <button class="bg-orange-primary hover:bg-orange-secondary text-white px-4 py-2 rounded-lg text-sm transition-colors">
                                评价商品
                            </button>
                        </div>
                    </div>

                    <!-- 商品2 -->
                    <div class="flex items-center space-x-4">
                        <img src="https://picsum.photos/80/80?random=11" alt="无线耳机" class="w-20 h-20 object-cover rounded-lg">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-800">无线降噪耳机</h3>
                            <p class="text-sm text-gray-500 mt-1">颜色：白色</p>
                            <div class="flex items-center mt-2">
                                <span class="text-orange-primary font-bold">¥899</span>
                                <span class="text-gray-400 text-sm ml-2">x1</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <button class="bg-orange-primary hover:bg-orange-secondary text-white px-4 py-2 rounded-lg text-sm transition-colors">
                                评价商品
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 订单总计 -->
                <div class="border-t mt-6 pt-4">
                    <div class="flex justify-between items-center">
                        <div class="flex space-x-4">
                            <button class="text-orange-primary hover:text-orange-secondary text-sm">再次购买</button>
                            <button class="text-gray-600 hover:text-gray-800 text-sm">查看详情</button>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-600">共2件商品，总计</div>
                            <div class="text-lg font-bold text-orange-primary">¥2,198</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单2 -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <!-- 订单头部 -->
            <div class="bg-gray-50 px-6 py-4 border-b">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">订单号：ORD202401002</span>
                        <span class="text-sm text-gray-600">下单时间：2024-01-18 14:20</span>
                    </div>
                    <span class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">配送中</span>
                </div>
            </div>

            <!-- 订单商品 -->
            <div class="p-6">
                <div class="space-y-4">
                    <!-- 商品 -->
                    <div class="flex items-center space-x-4">
                        <img src="https://picsum.photos/80/80?random=12" alt="运动背包" class="w-20 h-20 object-cover rounded-lg">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-800">专业运动背包</h3>
                            <p class="text-sm text-gray-500 mt-1">颜色：蓝色</p>
                            <div class="flex items-center mt-2">
                                <span class="text-orange-primary font-bold">¥299</span>
                                <span class="text-gray-400 text-sm ml-2">x1</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <button class="bg-orange-primary hover:bg-orange-secondary text-white px-4 py-2 rounded-lg text-sm transition-colors">
                                查看物流
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 物流信息 -->
                <div class="bg-blue-50 rounded-lg p-4 mt-4">
                    <div class="flex items-center">
                        <i class="fa fa-truck text-blue-600 mr-3"></i>
                        <div>
                            <div class="text-sm font-medium text-blue-800">您的包裹正在配送中</div>
                            <div class="text-xs text-blue-600 mt-1">预计今日18:00前送达，请保持手机畅通</div>
                        </div>
                    </div>
                </div>

                <!-- 订单总计 -->
                <div class="border-t mt-6 pt-4">
                    <div class="flex justify-between items-center">
                        <div class="flex space-x-4">
                            <button class="text-orange-primary hover:text-orange-secondary text-sm">确认收货</button>
                            <button class="text-gray-600 hover:text-gray-800 text-sm">查看详情</button>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-600">共1件商品，总计</div>
                            <div class="text-lg font-bold text-orange-primary">¥299</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单3 -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <!-- 订单头部 -->
            <div class="bg-gray-50 px-6 py-4 border-b">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">订单号：ORD202401003</span>
                        <span class="text-sm text-gray-600">下单时间：2024-01-20 09:15</span>
                    </div>
                    <span class="bg-red-100 text-red-800 text-sm px-3 py-1 rounded-full">待付款</span>
                </div>
            </div>

            <!-- 订单商品 -->
            <div class="p-6">
                <div class="space-y-4">
                    <!-- 商品 -->
                    <div class="flex items-center space-x-4">
                        <img src="https://picsum.photos/80/80?random=13" alt="智能音箱" class="w-20 h-20 object-cover rounded-lg">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-800">智能语音助手音箱</h3>
                            <p class="text-sm text-gray-500 mt-1">颜色：白色</p>
                            <div class="flex items-center mt-2">
                                <span class="text-orange-primary font-bold">¥399</span>
                                <span class="text-gray-400 text-sm ml-2">x1</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <button class="bg-orange-primary hover:bg-orange-secondary text-white px-4 py-2 rounded-lg text-sm transition-colors">
                                立即付款
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 付款提醒 -->
                <div class="bg-red-50 rounded-lg p-4 mt-4">
                    <div class="flex items-center">
                        <i class="fa fa-clock-o text-red-600 mr-3"></i>
                        <div>
                            <div class="text-sm font-medium text-red-800">订单将在23小时59分钟后自动取消</div>
                            <div class="text-xs text-red-600 mt-1">请尽快完成付款</div>
                        </div>
                    </div>
                </div>

                <!-- 订单总计 -->
                <div class="border-t mt-6 pt-4">
                    <div class="flex justify-between items-center">
                        <div class="flex space-x-4">
                            <button class="text-gray-600 hover:text-gray-800 text-sm">取消订单</button>
                            <button class="text-gray-600 hover:text-gray-800 text-sm">查看详情</button>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-600">共1件商品，总计</div>
                            <div class="text-lg font-bold text-orange-primary">¥399</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    <div class="flex justify-center mt-8">
        <nav class="flex space-x-2">
            <button class="px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50" disabled>
                上一页
            </button>
            <button class="px-3 py-2 text-white bg-orange-primary border border-orange-primary rounded-lg">
                1
            </button>
            <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                2
            </button>
            <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                3
            </button>
            <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                下一页
            </button>
        </nav>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 订单状态筛选
document.querySelectorAll('button').forEach(button => {
    if (button.textContent.includes('全部订单') || button.textContent.includes('待付款') || 
        button.textContent.includes('待发货') || button.textContent.includes('待收货') || 
        button.textContent.includes('待评价') || button.textContent.includes('已完成') || 
        button.textContent.includes('已取消')) {
        
        button.addEventListener('click', function() {
            // 移除所有活动状态
            document.querySelectorAll('button').forEach(btn => {
                if (btn.textContent.includes('订单') || btn.textContent.includes('待') || btn.textContent.includes('已')) {
                    btn.classList.remove('text-orange-primary', 'border-b-2', 'border-orange-primary', 'font-medium');
                    btn.classList.add('text-gray-600');
                }
            });
            
            // 添加活动状态到当前按钮
            this.classList.remove('text-gray-600');
            this.classList.add('text-orange-primary', 'border-b-2', 'border-orange-primary', 'font-medium');
            
            // 这里可以添加筛选订单的逻辑
            console.log('筛选订单:', this.textContent.trim());
        });
    }
});

// 订单操作
function payOrder(orderId) {
    alert('跳转到支付页面');
}

function cancelOrder(orderId) {
    if (confirm('确定要取消这个订单吗？')) {
        alert('订单取消功能需要连接到实际的数据库');
    }
}

function confirmReceipt(orderId) {
    if (confirm('确认已收到商品吗？')) {
        alert('确认收货功能需要连接到实际的数据库');
    }
}

function reviewProduct(productId) {
    alert('跳转到商品评价页面');
}

function viewLogistics(orderId) {
    alert('查看物流详情');
}

function reorder(orderId) {
    if (confirm('确定要再次购买这些商品吗？')) {
        alert('再次购买功能需要连接到实际的数据库');
    }
}
</script>
{% endblock %}

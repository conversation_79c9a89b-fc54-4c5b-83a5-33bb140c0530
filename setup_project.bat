@echo off
echo ========================================
echo    智能电商系统 - 模块化安装脚本
echo ========================================
echo.

echo 1. 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)
echo Python环境检查通过

echo.
echo 2. 检查MySQL服务...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告：未找到MySQL命令行工具，请确保MySQL已安装并启动
)

echo.
echo 3. 安装Python依赖包...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo 错误：依赖包安装失败
    pause
    exit /b 1
)
echo 依赖包安装完成

echo.
echo 4. 创建数据库（如果不存在）...
mysql -u root -p123456 -e "CREATE DATABASE IF NOT EXISTS shopping CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>nul
if %errorlevel% neq 0 (
    echo 警告：数据库创建可能失败，请手动创建数据库 'shopping'
) else (
    echo 数据库创建成功
)

echo.
echo 5. 创建前台应用模块...
cd frontend
python manage.py startapp users
python manage.py startapp products
python manage.py startapp cart
python manage.py startapp orders
python manage.py startapp payments
python manage.py startapp reviews
echo 前台应用模块创建完成

echo.
echo 6. 创建后台应用模块...
cd ..\backend
python manage.py startapp admin_panel
python manage.py startapp statistics
python manage.py startapp settings
echo 后台应用模块创建完成

echo.
echo 7. 初始化前台数据库...
cd ..\frontend
python manage.py makemigrations users products cart orders payments reviews
if %errorlevel% neq 0 (
    echo 错误：前台迁移文件创建失败
    pause
    exit /b 1
)

python manage.py migrate
if %errorlevel% neq 0 (
    echo 错误：前台数据库迁移失败
    pause
    exit /b 1
)
echo 前台数据库初始化完成

echo.
echo 8. 初始化后台数据库...
cd ..\backend
python manage.py makemigrations admin_panel statistics settings
if %errorlevel% neq 0 (
    echo 错误：后台迁移文件创建失败
    pause
    exit /b 1
)

python manage.py migrate
if %errorlevel% neq 0 (
    echo 错误：后台数据库迁移失败
    pause
    exit /b 1
)
echo 后台数据库初始化完成

echo.
echo 9. 创建示例数据...
cd ..\frontend
python manage.py init_data
if %errorlevel% neq 0 (
    echo 警告：示例数据创建可能失败，可以稍后手动添加
) else (
    echo 示例数据创建完成
)

echo.
echo 10. 创建媒体文件目录...
cd ..
if not exist "media" mkdir media
if not exist "media\products" mkdir media\products
if not exist "media\category" mkdir media\category
if not exist "media\banners" mkdir media\banners
if not exist "media\avatars" mkdir media\avatars
if not exist "media\reviews" mkdir media\reviews
echo 媒体文件目录创建完成

echo.
echo ========================================
echo        模块化系统安装完成！
echo ========================================
echo.
echo 系统模块结构：
echo 前台模块：users, products, cart, orders, payments, reviews
echo 后台模块：admin_panel, statistics, settings
echo.
echo 接下来您可以：
echo.
echo 1. 运行 start_servers.bat 启动服务
echo 2. 访问前台商城：http://127.0.0.1:8001/
echo 3. 访问后台管理：http://127.0.0.1:8003/dashboard/
echo.
echo 测试账号：
echo 前台用户：testuser / test123456
echo VIP用户：vipuser / vip123456
echo 后台管理：需要先注册管理员账号
echo.
echo 按任意键退出...
pause >nul

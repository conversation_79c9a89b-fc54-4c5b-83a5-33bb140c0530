{% extends 'base.html' %}

{% block title %}搜索结果 - 智能电商{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- 面包屑导航 -->
    {% if current_category %}
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'products:index' %}" class="text-gray-700 hover:text-orange-primary">
                    <i class="fa fa-home mr-2"></i>首页
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fa fa-angle-right text-gray-400 mx-2"></i>
                    <a href="{% url 'products:category_list' %}" class="text-gray-700 hover:text-orange-primary">分类</a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fa fa-angle-right text-gray-400 mx-2"></i>
                    <span class="text-gray-500">{{ current_category.name }}</span>
                </div>
            </li>
        </ol>
    </nav>
    {% endif %}

    <!-- 搜索结果头部 -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">
                    {% if current_category %}
                        {{ current_category.name }}
                    {% elif query %}
                        "{{ query }}" 的搜索结果
                    {% elif is_hot %}
                        热销商品
                    {% elif is_new %}
                        新品上市
                    {% else %}
                        商品列表
                    {% endif %}
                </h1>
                <p class="text-gray-600 mt-1">共找到 {{ total_count }} 件商品</p>
            </div>
            <div class="flex items-center space-x-4">
                <select class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-primary/30" onchange="changeSort(this.value)">
                    <option value="default" {% if sort_by == 'default' %}selected{% endif %}>默认排序</option>
                    <option value="price_asc" {% if sort_by == 'price_asc' %}selected{% endif %}>价格从低到高</option>
                    <option value="price_desc" {% if sort_by == 'price_desc' %}selected{% endif %}>价格从高到低</option>
                    <option value="rating" {% if sort_by == 'rating' %}selected{% endif %}>评分优先</option>
                    <option value="new" {% if sort_by == 'new' %}selected{% endif %}>最新上架</option>
                </select>
                <div class="flex border border-gray-300 rounded-lg">
                    <button class="px-3 py-2 bg-orange-primary text-white rounded-l-lg" onclick="toggleView('grid')">
                        <i class="fa fa-th"></i>
                    </button>
                    <button class="px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-r-lg" onclick="toggleView('list')">
                        <i class="fa fa-list"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- 筛选侧边栏 -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-xl shadow-sm p-6 sticky top-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">筛选条件</h3>
                
                <!-- 分类筛选 -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-3">商品分类</h4>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="category" value="" class="text-orange-primary focus:ring-orange-primary" {% if not current_category_id %}checked{% endif %}>
                            <span class="ml-2 text-sm text-gray-600">全部分类</span>
                        </label>
                        {% for category in categories %}
                        <label class="flex items-center">
                            <input type="radio" name="category" value="{{ category.id }}" class="text-orange-primary focus:ring-orange-primary" {% if current_category_id == category.id|stringformat:"s" %}checked{% endif %}>
                            <span class="ml-2 text-sm text-gray-600">{{ category.name }}</span>
                        </label>
                        {% endfor %}
                    </div>
                </div>

                <!-- 特色筛选 -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-3">特色商品</h4>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_hot" value="1" class="text-orange-primary focus:ring-orange-primary" {% if is_hot %}checked{% endif %}>
                            <span class="ml-2 text-sm text-gray-600">热销商品</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="is_new" value="1" class="text-orange-primary focus:ring-orange-primary" {% if is_new %}checked{% endif %}>
                            <span class="ml-2 text-sm text-gray-600">新品上市</span>
                        </label>
                    </div>
                </div>

                <!-- 品牌筛选 -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-3">品牌</h4>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="brand" value="" class="text-orange-primary focus:ring-orange-primary" {% if not current_brand %}checked{% endif %}>
                            <span class="ml-2 text-sm text-gray-600">全部品牌</span>
                        </label>
                        {% for brand in brands %}
                        <label class="flex items-center">
                            <input type="radio" name="brand" value="{{ brand.id }}" class="text-orange-primary focus:ring-orange-primary" {% if current_brand == brand.id|stringformat:"s" %}checked{% endif %}>
                            <span class="ml-2 text-sm text-gray-600">{{ brand.name }}</span>
                        </label>
                        {% endfor %}
                    </div>
                </div>

                <!-- 价格筛选 -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-3">价格区间</h4>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="price" value="" class="text-orange-primary focus:ring-orange-primary" checked>
                            <span class="ml-2 text-sm text-gray-600">不限</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="price" value="0-100" class="text-orange-primary focus:ring-orange-primary">
                            <span class="ml-2 text-sm text-gray-600">¥100以下</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="price" value="100-500" class="text-orange-primary focus:ring-orange-primary">
                            <span class="ml-2 text-sm text-gray-600">¥100-500</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="price" value="500-1000" class="text-orange-primary focus:ring-orange-primary">
                            <span class="ml-2 text-sm text-gray-600">¥500-1000</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="price" value="1000-5000" class="text-orange-primary focus:ring-orange-primary">
                            <span class="ml-2 text-sm text-gray-600">¥1000-5000</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="price" value="5000-" class="text-orange-primary focus:ring-orange-primary">
                            <span class="ml-2 text-sm text-gray-600">¥5000以上</span>
                        </label>
                    </div>
                    <div class="mt-3 pt-3 border-t border-gray-200">
                        <div class="flex space-x-2">
                            <input type="number" name="min_price" placeholder="最低价" value="{{ min_price }}" class="flex-1 border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-orange-primary/30">
                            <span class="text-gray-400">-</span>
                            <input type="number" name="max_price" placeholder="最高价" value="{{ max_price }}" class="flex-1 border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-orange-primary/30">
                        </div>
                        <button type="button" onclick="applyPriceFilter()" class="w-full mt-2 bg-orange-primary text-white py-1 rounded text-sm hover:bg-orange-secondary transition-colors">确定</button>
                    </div>
                </div>

                <button class="w-full bg-orange-primary hover:bg-orange-secondary text-white py-2 rounded-lg transition-colors" onclick="applyFilters()">
                    应用筛选
                </button>
            </div>
        </div>

        <!-- 商品列表 -->
        <div class="lg:col-span-3">
            {% if products %}
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6" id="product-grid">
                    {% for product in products %}
                    <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                        <div class="relative">
                            <a href="{% url 'products:detail' product.id %}">
                                <img src="{{ product.image }}" alt="{{ product.name }}" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                            </a>
                            {% if product.is_hot %}
                            <div class="absolute top-3 left-3">
                                <span class="bg-orange-primary text-white text-xs font-medium py-1 px-2 rounded">热销</span>
                            </div>
                            {% endif %}
                            {% if product.is_new %}
                            <div class="absolute top-3 {% if product.is_hot %}left-16{% else %}left-3{% endif %}">
                                <span class="bg-green-500 text-white text-xs font-medium py-1 px-2 rounded">新品</span>
                            </div>
                            {% endif %}
                            <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors" onclick="toggleFavorite({{ product.id }})">
                                <i class="fa fa-heart-o"></i>
                            </button>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <div class="flex text-yellow-400">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= product.rating|default:4 %}
                                            <i class="fa fa-star"></i>
                                        {% else %}
                                            <i class="fa fa-star-o"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <span class="text-gray-500 text-sm ml-2">{{ product.rating }} ({{ product.reviews_count }})</span>
                            </div>
                            <h3 class="font-medium text-gray-800 mb-1">
                                <a href="{% url 'products:detail' product.id %}" class="hover:text-orange-primary transition-colors">
                                    {{ product.name }}
                                </a>
                            </h3>
                            <p class="text-gray-500 text-sm mb-3">{{ product.description|truncatechars:50 }}</p>
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="text-orange-primary font-bold">¥{{ product.price }}</span>
                                    {% if product.original_price and product.original_price > product.price %}
                                    <span class="text-gray-400 line-through text-sm ml-2">¥{{ product.original_price }}</span>
                                    {% endif %}
                                </div>
                                <button class="bg-orange-primary hover:bg-orange-secondary text-white w-10 h-10 rounded-full flex items-center justify-center transition-colors" onclick="addToCart({{ product.id }})">
                                    <i class="fa fa-shopping-cart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- 分页 -->
                {% if page_obj.has_other_pages %}
                <div class="flex justify-center mt-8">
                    <nav class="flex space-x-2">
                        {% if page_obj.has_previous %}
                        <a href="?{% if query %}q={{ query }}&{% endif %}{% if current_category %}category={{ current_category }}&{% endif %}{% if current_brand %}brand={{ current_brand }}&{% endif %}{% if sort_by %}sort={{ sort_by }}&{% endif %}page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                            上一页
                        </a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <span class="px-3 py-2 text-white bg-orange-primary border border-orange-primary rounded-lg">
                                {{ num }}
                            </span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?{% if query %}q={{ query }}&{% endif %}{% if current_category %}category={{ current_category }}&{% endif %}{% if current_brand %}brand={{ current_brand }}&{% endif %}{% if sort_by %}sort={{ sort_by }}&{% endif %}page={{ num }}" class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                                {{ num }}
                            </a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <a href="?{% if query %}q={{ query }}&{% endif %}{% if current_category %}category={{ current_category }}&{% endif %}{% if current_brand %}brand={{ current_brand }}&{% endif %}{% if sort_by %}sort={{ sort_by }}&{% endif %}page={{ page_obj.next_page_number }}" class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                            下一页
                        </a>
                        {% endif %}
                    </nav>
                </div>
                {% endif %}
            {% else %}
                <!-- 无搜索结果 -->
                <div class="text-center py-16">
                    <i class="fa fa-search text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">没有找到相关商品</h3>
                    <p class="text-gray-500 mb-6">
                        {% if query %}
                            抱歉，没有找到与 "{{ query }}" 相关的商品
                        {% else %}
                            当前筛选条件下没有商品
                        {% endif %}
                    </p>
                    <div class="space-x-4">
                        <a href="{% url 'products:index' %}" class="bg-orange-primary hover:bg-orange-secondary text-white px-6 py-2 rounded-lg transition-colors">
                            返回首页
                        </a>
                        <button class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors" onclick="clearFilters()">
                            清除筛选
                        </button>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function changeSort(sortValue) {
    const url = new URL(window.location);
    url.searchParams.set('sort', sortValue);
    window.location.href = url.toString();
}

function applyFilters() {
    const url = new URL(window.location);

    // 获取选中的分类
    const selectedCategory = document.querySelector('input[name="category"]:checked').value;
    if (selectedCategory) {
        url.searchParams.set('category', selectedCategory);
    } else {
        url.searchParams.delete('category');
    }

    // 获取选中的品牌
    const selectedBrand = document.querySelector('input[name="brand"]:checked').value;
    if (selectedBrand) {
        url.searchParams.set('brand', selectedBrand);
    } else {
        url.searchParams.delete('brand');
    }

    // 获取特色筛选
    const isHot = document.querySelector('input[name="is_hot"]:checked');
    if (isHot) {
        url.searchParams.set('is_hot', '1');
    } else {
        url.searchParams.delete('is_hot');
    }

    const isNew = document.querySelector('input[name="is_new"]:checked');
    if (isNew) {
        url.searchParams.set('is_new', '1');
    } else {
        url.searchParams.delete('is_new');
    }

    // 获取选中的价格区间
    const selectedPrice = document.querySelector('input[name="price"]:checked');
    if (selectedPrice && selectedPrice.value) {
        const [min, max] = selectedPrice.value.split('-');
        if (min) url.searchParams.set('min_price', min);
        if (max) url.searchParams.set('max_price', max);
    } else {
        url.searchParams.delete('min_price');
        url.searchParams.delete('max_price');
    }

    url.searchParams.delete('page'); // 重置页码
    window.location.href = url.toString();
}

function applyPriceFilter() {
    const url = new URL(window.location);
    const minPrice = document.querySelector('input[name="min_price"]').value;
    const maxPrice = document.querySelector('input[name="max_price"]').value;

    if (minPrice) {
        url.searchParams.set('min_price', minPrice);
    } else {
        url.searchParams.delete('min_price');
    }

    if (maxPrice) {
        url.searchParams.set('max_price', maxPrice);
    } else {
        url.searchParams.delete('max_price');
    }

    url.searchParams.delete('page');
    window.location.href = url.toString();
}

function toggleView(viewType) {
    const grid = document.getElementById('product-grid');
    if (viewType === 'list') {
        grid.className = 'space-y-4';
        // 这里可以添加列表视图的样式
    } else {
        grid.className = 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6';
    }
}

function clearFilters() {
    const url = new URL(window.location);
    url.searchParams.delete('category');
    url.searchParams.delete('brand');
    url.searchParams.delete('is_hot');
    url.searchParams.delete('is_new');
    url.searchParams.delete('min_price');
    url.searchParams.delete('max_price');
    url.searchParams.delete('sort');
    url.searchParams.delete('page');
    window.location.href = url.toString();
}

function toggleFavorite(productId) {
    // 收藏功能
    alert('收藏功能需要登录');
}

function addToCart(productId) {
    // 添加到购物车
    alert('添加到购物车功能');
}
</script>
{% endblock %}

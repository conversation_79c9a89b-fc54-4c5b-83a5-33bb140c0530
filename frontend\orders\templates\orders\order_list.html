{% extends 'base.html' %}

{% block title %}我的订单 - 智能电商{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- 页面标题 -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">我的订单</h1>
        <p class="text-gray-600">共 {{ total_orders }} 个订单</p>
    </div>

    <!-- 订单状态筛选 -->
    <div class="mb-6">
        <div class="flex flex-wrap gap-2">
            <a href="{% url 'orders:list' %}" 
               class="px-4 py-2 rounded-lg text-sm font-medium transition-colors {% if not current_status %}bg-orange-primary text-white{% else %}bg-gray-100 text-gray-700 hover:bg-gray-200{% endif %}">
                全部订单
            </a>
            {% for status_code, status_name in status_choices %}
            <a href="{% url 'orders:list' %}?status={{ status_code }}" 
               class="px-4 py-2 rounded-lg text-sm font-medium transition-colors {% if current_status == status_code %}bg-orange-primary text-white{% else %}bg-gray-100 text-gray-700 hover:bg-gray-200{% endif %}">
                {{ status_name }}
            </a>
            {% endfor %}
        </div>
    </div>

    <!-- 订单列表 -->
    {% if orders %}
    <div class="space-y-6">
        {% for order in orders %}
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <!-- 订单头部 -->
            <div class="bg-gray-50 px-6 py-4 border-b">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">订单号：</span>
                        <span class="font-medium text-gray-900">{{ order.order_number }}</span>
                        <span class="text-sm text-gray-600">下单时间：{{ order.created_at }}</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="px-3 py-1 rounded-full text-sm font-medium
                            {% if order.status == 'pending' %}bg-yellow-100 text-yellow-800
                            {% elif order.status == 'paid' %}bg-blue-100 text-blue-800
                            {% elif order.status == 'shipped' %}bg-purple-100 text-purple-800
                            {% elif order.status == 'completed' %}bg-green-100 text-green-800
                            {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ order.status_display }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- 订单商品 -->
            <div class="p-6">
                <div class="space-y-4">
                    {% for item in order.items %}
                    <div class="flex items-center space-x-4">
                        <img src="{{ item.product.image }}" alt="{{ item.product.name }}" 
                             class="w-16 h-16 object-cover rounded-lg">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-900">{{ item.product.name }}</h3>
                            <div class="flex items-center mt-1 space-x-4">
                                <span class="text-orange-primary font-medium">¥{{ item.price }}</span>
                                <span class="text-gray-500 text-sm">x{{ item.quantity }}</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-medium text-gray-900">¥{{ item.price|floatformat:2 }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- 订单底部 -->
            <div class="bg-gray-50 px-6 py-4 border-t">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        {% if order.status == 'pending' %}
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white px-4 py-2 rounded-lg text-sm transition-colors"
                                onclick="payOrder('{{ order.id }}')">
                            立即付款
                        </button>
                        <button class="border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 rounded-lg text-sm transition-colors"
                                onclick="cancelOrder('{{ order.id }}')">
                            取消订单
                        </button>
                        {% elif order.status == 'shipped' %}
                        <button class="bg-orange-primary hover:bg-orange-secondary text-white px-4 py-2 rounded-lg text-sm transition-colors"
                                onclick="confirmReceipt('{{ order.id }}')">
                            确认收货
                        </button>
                        <button class="border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 rounded-lg text-sm transition-colors"
                                onclick="trackOrder('{{ order.id }}')">
                            查看物流
                        </button>
                        {% elif order.status == 'completed' %}
                        <button class="border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 rounded-lg text-sm transition-colors"
                                onclick="reviewOrder('{{ order.id }}')">
                            评价商品
                        </button>
                        <button class="border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 rounded-lg text-sm transition-colors"
                                onclick="buyAgain('{{ order.id }}')">
                            再次购买
                        </button>
                        {% endif %}
                        
                        <a href="{% url 'orders:detail' order.id %}" 
                           class="text-orange-primary hover:text-orange-secondary text-sm">
                            查看详情
                        </a>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-600">订单总额</div>
                        <div class="text-xl font-bold text-orange-primary">¥{{ order.total_amount }}</div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- 分页 -->
    <div class="mt-8 flex justify-center">
        <nav class="flex space-x-2">
            <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                上一页
            </button>
            <button class="px-3 py-2 text-white bg-orange-primary border border-orange-primary rounded-lg">
                1
            </button>
            <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                2
            </button>
            <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                下一页
            </button>
        </nav>
    </div>
    {% else %}
    <!-- 空订单 -->
    <div class="text-center py-16">
        <i class="fa fa-list-alt text-6xl text-gray-300 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">暂无订单</h3>
        <p class="text-gray-500 mb-6">您还没有任何订单记录</p>
        <a href="{% url 'products:index' %}" class="bg-orange-primary hover:bg-orange-secondary text-white px-6 py-3 rounded-lg transition-colors">
            去购物
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// 支付订单
function payOrder(orderId) {
    if (confirm('确定要支付这个订单吗？')) {
        alert('跳转到支付页面');
        // 这里可以跳转到支付页面
    }
}

// 取消订单
function cancelOrder(orderId) {
    const reason = prompt('请输入取消原因：');
    if (reason) {
        if (confirm('确定要取消这个订单吗？')) {
            alert('订单已取消');
            location.reload();
        }
    }
}

// 确认收货
function confirmReceipt(orderId) {
    if (confirm('确定已收到商品吗？确认后将无法撤销。')) {
        alert('确认收货成功');
        location.reload();
    }
}

// 查看物流
function trackOrder(orderId) {
    alert('查看物流信息');
    // 这里可以打开物流跟踪页面
}

// 评价商品
function reviewOrder(orderId) {
    alert('跳转到评价页面');
    // 这里可以跳转到评价页面
}

// 再次购买
function buyAgain(orderId) {
    if (confirm('确定要将这些商品加入购物车吗？')) {
        alert('商品已加入购物车');
    }
}
</script>
{% endblock %}

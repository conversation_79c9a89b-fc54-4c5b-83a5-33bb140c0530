{% extends 'management/base.html' %}

{% block title %}仪表盘 - 智能电商管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-tachometer-alt me-2"></i>仪表盘</h1>
    <div class="text-muted">
        <i class="fas fa-clock me-1"></i>{{ "now"|date:"Y-m-d H:i" }}
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h3>{{ total_users }}</h3>
                <p class="mb-0">总用户数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-box fa-2x mb-2"></i>
                <h3>{{ total_products }}</h3>
                <p class="mb-0">商品总数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                <h3>{{ total_orders }}</h3>
                <p class="mb-0">订单总数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-yen-sign fa-2x mb-2"></i>
                <h3>¥{{ total_sales|floatformat:2 }}</h3>
                <p class="mb-0">总销售额</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 订单状态统计 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie me-2"></i>订单状态统计</h5>
            </div>
            <div class="card-body">
                {% for stat in order_stats %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>
                        {% if stat.status == 'pending' %}
                            <i class="fas fa-clock text-warning"></i> 待支付
                        {% elif stat.status == 'paid' %}
                            <i class="fas fa-check text-success"></i> 已支付
                        {% elif stat.status == 'shipped' %}
                            <i class="fas fa-truck text-info"></i> 已发货
                        {% elif stat.status == 'delivered' %}
                            <i class="fas fa-box text-primary"></i> 已收货
                        {% elif stat.status == 'completed' %}
                            <i class="fas fa-check-circle text-success"></i> 已完成
                        {% elif stat.status == 'cancelled' %}
                            <i class="fas fa-times text-danger"></i> 已取消
                        {% endif %}
                    </span>
                    <span class="badge bg-primary">{{ stat.count }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- 热门商品 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-fire me-2"></i>热门商品</h5>
            </div>
            <div class="card-body">
                {% for product in hot_products %}
                <div class="d-flex align-items-center mb-3">
                    <img src="{{ product.main_image.url }}" alt="{{ product.name }}" 
                         class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">{{ product.name }}</h6>
                        <small class="text-muted">销量: {{ product.sales }} | 库存: {{ product.stock }}</small>
                    </div>
                    <span class="text-primary fw-bold">¥{{ product.price }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- 最新订单 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="fas fa-list me-2"></i>最新订单</h5>
        <a href="{% url 'management:order_list' %}" class="btn btn-outline-primary btn-sm">查看全部</a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>订单号</th>
                        <th>用户</th>
                        <th>金额</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in recent_orders %}
                    <tr>
                        <td>{{ order.order_no }}</td>
                        <td>{{ order.user.username }}</td>
                        <td>¥{{ order.final_amount }}</td>
                        <td>
                            {% if order.status == 'pending' %}
                                <span class="badge bg-warning">待支付</span>
                            {% elif order.status == 'paid' %}
                                <span class="badge bg-success">已支付</span>
                            {% elif order.status == 'shipped' %}
                                <span class="badge bg-info">已发货</span>
                            {% elif order.status == 'delivered' %}
                                <span class="badge bg-primary">已收货</span>
                            {% elif order.status == 'completed' %}
                                <span class="badge bg-success">已完成</span>
                            {% elif order.status == 'cancelled' %}
                                <span class="badge bg-danger">已取消</span>
                            {% endif %}
                        </td>
                        <td>{{ order.created_at|date:"m-d H:i" }}</td>
                        <td>
                            <a href="{% url 'management:order_detail' order.id %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center text-muted">暂无订单</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 实时更新时间
    function updateTime() {
        var now = new Date();
        var timeString = now.getFullYear() + '-' + 
                        String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                        String(now.getDate()).padStart(2, '0') + ' ' +
                        String(now.getHours()).padStart(2, '0') + ':' + 
                        String(now.getMinutes()).padStart(2, '0');
        $('.text-muted .fas.fa-clock').parent().html('<i class="fas fa-clock me-1"></i>' + timeString);
    }
    
    // 每分钟更新一次时间
    setInterval(updateTime, 60000);
});
</script>
{% endblock %}

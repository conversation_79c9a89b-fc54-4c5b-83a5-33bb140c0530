{% extends 'base.html' %}

{% block title %}购物车 - 智能电商{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">购物车</h1>
        <span class="ml-4 text-gray-500">(3件商品)</span>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 购物车商品列表 -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                <!-- 表头 -->
                <div class="bg-gray-50 px-6 py-4 border-b">
                    <div class="flex items-center">
                        <input type="checkbox" id="selectAll" class="w-4 h-4 text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                        <label for="selectAll" class="ml-3 text-sm font-medium text-gray-700">全选</label>
                        <span class="ml-auto text-sm text-gray-500">共3件商品</span>
                    </div>
                </div>

                <!-- 商品列表 -->
                <div class="divide-y divide-gray-100">
                    <!-- 商品1 -->
                    <div class="p-6 flex items-center space-x-4">
                        <input type="checkbox" class="w-4 h-4 text-orange-primary focus:ring-orange-primary border-gray-300 rounded item-checkbox">
                        <img src="https://picsum.photos/100/100?random=10" alt="智能手表" class="w-20 h-20 object-cover rounded-lg">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-800">智能手表 Pro</h3>
                            <p class="text-sm text-gray-500 mt-1">颜色：黑色 | 尺寸：42mm</p>
                            <div class="flex items-center mt-2">
                                <span class="text-orange-primary font-bold">¥1,299</span>
                                <span class="text-gray-400 line-through text-sm ml-2">¥1,599</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <button class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50" onclick="decreaseQuantity(1)">
                                <i class="fa fa-minus text-xs"></i>
                            </button>
                            <span class="w-12 text-center" id="quantity-1">1</span>
                            <button class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50" onclick="increaseQuantity(1)">
                                <i class="fa fa-plus text-xs"></i>
                            </button>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-gray-800">¥1,299</div>
                            <button class="text-red-500 hover:text-red-700 text-sm mt-1" onclick="removeItem(1)">
                                <i class="fa fa-trash mr-1"></i>删除
                            </button>
                        </div>
                    </div>

                    <!-- 商品2 -->
                    <div class="p-6 flex items-center space-x-4">
                        <input type="checkbox" class="w-4 h-4 text-orange-primary focus:ring-orange-primary border-gray-300 rounded item-checkbox">
                        <img src="https://picsum.photos/100/100?random=11" alt="无线耳机" class="w-20 h-20 object-cover rounded-lg">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-800">无线降噪耳机</h3>
                            <p class="text-sm text-gray-500 mt-1">颜色：白色</p>
                            <div class="flex items-center mt-2">
                                <span class="text-orange-primary font-bold">¥899</span>
                                <span class="text-gray-400 line-through text-sm ml-2">¥1,099</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <button class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50" onclick="decreaseQuantity(2)">
                                <i class="fa fa-minus text-xs"></i>
                            </button>
                            <span class="w-12 text-center" id="quantity-2">2</span>
                            <button class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50" onclick="increaseQuantity(2)">
                                <i class="fa fa-plus text-xs"></i>
                            </button>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-gray-800">¥1,798</div>
                            <button class="text-red-500 hover:text-red-700 text-sm mt-1" onclick="removeItem(2)">
                                <i class="fa fa-trash mr-1"></i>删除
                            </button>
                        </div>
                    </div>

                    <!-- 商品3 -->
                    <div class="p-6 flex items-center space-x-4">
                        <input type="checkbox" class="w-4 h-4 text-orange-primary focus:ring-orange-primary border-gray-300 rounded item-checkbox">
                        <img src="https://picsum.photos/100/100?random=12" alt="运动背包" class="w-20 h-20 object-cover rounded-lg">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-800">专业运动背包</h3>
                            <p class="text-sm text-gray-500 mt-1">颜色：蓝色</p>
                            <div class="flex items-center mt-2">
                                <span class="text-orange-primary font-bold">¥299</span>
                                <span class="text-gray-400 line-through text-sm ml-2">¥399</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <button class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50" onclick="decreaseQuantity(3)">
                                <i class="fa fa-minus text-xs"></i>
                            </button>
                            <span class="w-12 text-center" id="quantity-3">1</span>
                            <button class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50" onclick="increaseQuantity(3)">
                                <i class="fa fa-plus text-xs"></i>
                            </button>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-gray-800">¥299</div>
                            <button class="text-red-500 hover:text-red-700 text-sm mt-1" onclick="removeItem(3)">
                                <i class="fa fa-trash mr-1"></i>删除
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 底部操作 -->
                <div class="bg-gray-50 px-6 py-4 border-t">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <button class="text-red-500 hover:text-red-700 text-sm" onclick="removeSelected()">
                                <i class="fa fa-trash mr-1"></i>删除选中
                            </button>
                            <button class="text-orange-primary hover:text-orange-secondary text-sm">
                                <i class="fa fa-heart mr-1"></i>移入收藏夹
                            </button>
                        </div>
                        <div class="text-sm text-gray-500">
                            已选择 <span id="selectedCount">0</span> 件商品
                        </div>
                    </div>
                </div>
            </div>

            <!-- 推荐商品 -->
            <div class="mt-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">猜你喜欢</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <img src="https://picsum.photos/200/200?random=20" alt="推荐商品" class="w-full h-32 object-cover">
                        <div class="p-3">
                            <h4 class="text-sm font-medium text-gray-800 mb-1">蓝牙音箱</h4>
                            <div class="text-orange-primary font-bold text-sm">¥199</div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <img src="https://picsum.photos/200/200?random=21" alt="推荐商品" class="w-full h-32 object-cover">
                        <div class="p-3">
                            <h4 class="text-sm font-medium text-gray-800 mb-1">手机支架</h4>
                            <div class="text-orange-primary font-bold text-sm">¥39</div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <img src="https://picsum.photos/200/200?random=22" alt="推荐商品" class="w-full h-32 object-cover">
                        <div class="p-3">
                            <h4 class="text-sm font-medium text-gray-800 mb-1">数据线</h4>
                            <div class="text-orange-primary font-bold text-sm">¥29</div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <img src="https://picsum.photos/200/200?random=23" alt="推荐商品" class="w-full h-32 object-cover">
                        <div class="p-3">
                            <h4 class="text-sm font-medium text-gray-800 mb-1">保护壳</h4>
                            <div class="text-orange-primary font-bold text-sm">¥49</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单摘要 -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-xl shadow-sm p-6 sticky top-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">订单摘要</h3>
                
                <div class="space-y-3 mb-6">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">商品总价</span>
                        <span class="text-gray-800">¥3,396</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">运费</span>
                        <span class="text-green-600">免费</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">优惠券</span>
                        <span class="text-orange-primary">-¥50</span>
                    </div>
                    <div class="border-t pt-3">
                        <div class="flex justify-between">
                            <span class="font-semibold text-gray-800">总计</span>
                            <span class="font-bold text-xl text-orange-primary">¥3,346</span>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-600">优惠券</span>
                        <button class="text-orange-primary text-sm hover:text-orange-secondary">选择优惠券</button>
                    </div>
                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-3">
                        <div class="flex items-center">
                            <i class="fa fa-ticket text-orange-primary mr-2"></i>
                            <span class="text-sm text-orange-700">满300减50优惠券</span>
                        </div>
                    </div>
                </div>

                <button class="w-full bg-orange-primary hover:bg-orange-secondary text-white py-3 rounded-lg font-medium transition-colors mb-3">
                    去结算 (3)
                </button>
                
                <div class="text-center">
                    <a href="{% url 'products:index' %}" class="text-orange-primary hover:text-orange-secondary text-sm">
                        <i class="fa fa-arrow-left mr-1"></i>继续购物
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全选功能
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.item-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateSelectedCount();
});

// 单个商品选择
document.querySelectorAll('.item-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectedCount);
});

function updateSelectedCount() {
    const selectedItems = document.querySelectorAll('.item-checkbox:checked').length;
    document.getElementById('selectedCount').textContent = selectedItems;
    
    const selectAllCheckbox = document.getElementById('selectAll');
    const totalItems = document.querySelectorAll('.item-checkbox').length;
    selectAllCheckbox.checked = selectedItems === totalItems;
}

// 数量调整
function increaseQuantity(itemId) {
    const quantityElement = document.getElementById(`quantity-${itemId}`);
    let quantity = parseInt(quantityElement.textContent);
    quantity++;
    quantityElement.textContent = quantity;
    updateItemTotal(itemId, quantity);
}

function decreaseQuantity(itemId) {
    const quantityElement = document.getElementById(`quantity-${itemId}`);
    let quantity = parseInt(quantityElement.textContent);
    if (quantity > 1) {
        quantity--;
        quantityElement.textContent = quantity;
        updateItemTotal(itemId, quantity);
    }
}

function updateItemTotal(itemId, quantity) {
    // 这里应该根据商品ID和数量更新小计
    // 暂时使用静态逻辑
    console.log(`更新商品${itemId}的数量为${quantity}`);
}

// 删除商品
function removeItem(itemId) {
    if (confirm('确定要删除这件商品吗？')) {
        // 这里应该发送请求删除商品
        console.log(`删除商品${itemId}`);
    }
}

// 删除选中商品
function removeSelected() {
    const selectedItems = document.querySelectorAll('.item-checkbox:checked');
    if (selectedItems.length === 0) {
        alert('请先选择要删除的商品');
        return;
    }
    
    if (confirm(`确定要删除选中的${selectedItems.length}件商品吗？`)) {
        // 这里应该发送请求删除选中商品
        console.log('删除选中商品');
    }
}

// 初始化
updateSelectedCount();
</script>
{% endblock %}

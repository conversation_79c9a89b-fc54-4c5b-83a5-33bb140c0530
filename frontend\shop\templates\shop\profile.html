{% extends 'shop/base.html' %}

{% block title %}个人中心 - 智能电商{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="row">
        <!-- 侧边栏 -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    {% if profile.avatar %}
                    <img src="{{ profile.avatar.url }}" alt="头像" class="rounded-circle mb-3" style="width: 100px; height: 100px; object-fit: cover;">
                    {% else %}
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 100px; height: 100px;">
                        <i class="fas fa-user fa-2x text-white"></i>
                    </div>
                    {% endif %}
                    <h5>{{ user.username }}</h5>
                    {% if profile.is_vip %}
                    <span class="badge bg-warning text-dark">VIP会员</span>
                    {% else %}
                    <span class="badge bg-secondary">普通会员</span>
                    {% endif %}
                </div>
            </div>
            
            <div class="list-group mt-3">
                <a href="#profile-info" class="list-group-item list-group-item-action active" data-bs-toggle="tab">
                    <i class="fas fa-user me-2"></i>个人资料
                </a>
                <a href="#account-info" class="list-group-item list-group-item-action" data-bs-toggle="tab">
                    <i class="fas fa-wallet me-2"></i>账户信息
                </a>
                <a href="#password-change" class="list-group-item list-group-item-action" data-bs-toggle="tab">
                    <i class="fas fa-lock me-2"></i>修改密码
                </a>
                <a href="{% url 'shop:order_list' %}" class="list-group-item list-group-item-action">
                    <i class="fas fa-shopping-bag me-2"></i>我的订单
                </a>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="col-md-9">
            <div class="tab-content">
                <!-- 个人资料 -->
                <div class="tab-pane fade show active" id="profile-info">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-user me-2"></i>个人资料</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" enctype="multipart/form-data">
                                {% csrf_token %}
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">用户名</label>
                                        <input type="text" class="form-control" value="{{ user.username }}" readonly>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">邮箱</label>
                                        <input type="email" class="form-control" name="email" value="{{ user.email }}">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">姓</label>
                                        <input type="text" class="form-control" name="last_name" value="{{ user.last_name }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">名</label>
                                        <input type="text" class="form-control" name="first_name" value="{{ user.first_name }}">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">手机号</label>
                                        <input type="tel" class="form-control" name="phone" value="{{ profile.phone }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">性别</label>
                                        <select class="form-select" name="gender">
                                            <option value="">请选择</option>
                                            <option value="male" {% if profile.gender == 'male' %}selected{% endif %}>男</option>
                                            <option value="female" {% if profile.gender == 'female' %}selected{% endif %}>女</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">生日</label>
                                        <input type="date" class="form-control" name="birthday" value="{{ profile.birthday|date:'Y-m-d' }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">头像</label>
                                        <input type="file" class="form-control" name="avatar" accept="image/*">
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>保存修改
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 账户信息 -->
                <div class="tab-pane fade" id="account-info">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-wallet me-2"></i>账户信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <i class="fas fa-wallet fa-2x mb-2"></i>
                                            <h4>¥{{ profile.balance|floatformat:2 }}</h4>
                                            <p class="mb-0">账户余额</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <i class="fas fa-crown fa-2x mb-2"></i>
                                            <h4>
                                                {% if profile.is_vip %}
                                                VIP会员
                                                {% else %}
                                                普通会员
                                                {% endif %}
                                            </h4>
                                            <p class="mb-0">会员等级</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <h6>会员特权</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-shipping-fast me-2 text-primary"></i>免费配送</span>
                                        {% if profile.is_vip %}
                                        <span class="badge bg-success">已享受</span>
                                        {% else %}
                                        <span class="badge bg-secondary">升级VIP</span>
                                        {% endif %}
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-percent me-2 text-primary"></i>专属折扣</span>
                                        {% if profile.is_vip %}
                                        <span class="badge bg-success">已享受</span>
                                        {% else %}
                                        <span class="badge bg-secondary">升级VIP</span>
                                        {% endif %}
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-headset me-2 text-primary"></i>专属客服</span>
                                        {% if profile.is_vip %}
                                        <span class="badge bg-success">已享受</span>
                                        {% else %}
                                        <span class="badge bg-secondary">升级VIP</span>
                                        {% endif %}
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 修改密码 -->
                <div class="tab-pane fade" id="password-change">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-lock me-2"></i>修改密码</h5>
                        </div>
                        <div class="card-body">
                            <form id="passwordForm">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label class="form-label">当前密码</label>
                                    <input type="password" class="form-control" name="current_password" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">新密码</label>
                                    <input type="password" class="form-control" name="new_password" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">确认新密码</label>
                                    <input type="password" class="form-control" name="confirm_password" required>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-key me-2"></i>修改密码
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 修改密码表单提交
    $('#passwordForm').submit(function(e) {
        e.preventDefault();
        
        var newPassword = $('input[name="new_password"]').val();
        var confirmPassword = $('input[name="confirm_password"]').val();
        
        if (newPassword !== confirmPassword) {
            alert('两次输入的新密码不一致');
            return;
        }
        
        if (newPassword.length < 6) {
            alert('密码长度至少6位');
            return;
        }
        
        // 这里可以添加AJAX请求来修改密码
        alert('密码修改功能待实现');
    });

    // 头像预览
    $('input[name="avatar"]').change(function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                // 可以添加头像预览功能
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
{% endblock %}

{% extends 'admin_panel/base.html' %}

{% block title %}商品分类管理 - 智能电商管理后台{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="fas fa-tags me-2"></i>商品分类管理</h1>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
            <i class="fas fa-plus me-2"></i>添加分类
        </button>
    </div>
</div>

<div class="card content-card">
    <div class="card-header">
        <h5><i class="fas fa-list me-2"></i>分类列表</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>分类名称</th>
                        <th>父分类</th>
                        <th>排序</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>
                            <i class="fas fa-mobile-alt me-2 text-primary"></i>
                            电子产品
                        </td>
                        <td>-</td>
                        <td>1</td>
                        <td><span class="badge bg-success">启用</span></td>
                        <td>2024-01-01</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>
                            <i class="fas fa-mobile-alt me-2 text-secondary"></i>
                            &nbsp;&nbsp;手机通讯
                        </td>
                        <td>电子产品</td>
                        <td>1</td>
                        <td><span class="badge bg-success">启用</span></td>
                        <td>2024-01-01</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>
                            <i class="fas fa-laptop me-2 text-secondary"></i>
                            &nbsp;&nbsp;电脑办公
                        </td>
                        <td>电子产品</td>
                        <td>2</td>
                        <td><span class="badge bg-success">启用</span></td>
                        <td>2024-01-01</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>
                            <i class="fas fa-tshirt me-2 text-primary"></i>
                            服装鞋帽
                        </td>
                        <td>-</td>
                        <td>2</td>
                        <td><span class="badge bg-success">启用</span></td>
                        <td>2024-01-01</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 添加分类模态框 -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>添加商品分类</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCategoryForm">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">分类名称</label>
                        <input type="text" class="form-control" id="categoryName" required>
                    </div>
                    <div class="mb-3">
                        <label for="parentCategory" class="form-label">父分类</label>
                        <select class="form-select" id="parentCategory">
                            <option value="">选择父分类（可选）</option>
                            <option value="1">电子产品</option>
                            <option value="4">服装鞋帽</option>
                            <option value="5">家居用品</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="sortOrder" class="form-label">排序</label>
                        <input type="number" class="form-control" id="sortOrder" value="0">
                    </div>
                    <div class="mb-3">
                        <label for="categoryIcon" class="form-label">分类图标</label>
                        <input type="text" class="form-control" id="categoryIcon" placeholder="例如：fas fa-mobile-alt">
                        <div class="form-text">请输入Font Awesome图标类名</div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isActive" checked>
                            <label class="form-check-label" for="isActive">
                                启用分类
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addCategory()">
                    <i class="fas fa-save me-2"></i>保存
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function addCategory() {
    // 获取表单数据
    const name = document.getElementById('categoryName').value;
    const parentId = document.getElementById('parentCategory').value;
    const sortOrder = document.getElementById('sortOrder').value;
    const icon = document.getElementById('categoryIcon').value;
    const isActive = document.getElementById('isActive').checked;
    
    if (!name.trim()) {
        alert('请输入分类名称');
        return;
    }
    
    // 这里应该发送AJAX请求到后端
    alert('分类添加功能需要连接到实际的数据库');
    
    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('addCategoryModal'));
    modal.hide();
    
    // 重置表单
    document.getElementById('addCategoryForm').reset();
}

// 删除分类
function deleteCategory(id) {
    if (confirm('确定要删除这个分类吗？')) {
        alert('删除功能需要连接到实际的数据库');
    }
}

// 编辑分类
function editCategory(id) {
    alert('编辑功能需要连接到实际的数据库');
}
</script>
{% endblock %}

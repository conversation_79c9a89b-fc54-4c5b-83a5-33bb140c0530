from django.urls import path
from . import views

app_name = 'products'

urlpatterns = [
    # 首页
    path('', views.index, name='index'),
    
    # 商品
    path('list/', views.product_list, name='list'),
    path('<int:product_id>/', views.product_detail, name='detail'),
    
    # 分类和品牌
    path('categories/', views.category_list, name='category_list'),
    path('brands/', views.brand_list, name='brand_list'),
    
    # 收藏
    path('toggle-favorite/', views.toggle_favorite, name='toggle_favorite'),
    path('favorites/', views.favorite_list, name='favorite_list'),
    
    # 搜索
    path('search/', views.search, name='search'),
    path('search-suggestions/', views.search_suggestions, name='search_suggestions'),
]

from django.contrib import admin
from .models import Review, ReviewImage


class ReviewImageInline(admin.TabularInline):
    model = ReviewImage
    extra = 0


@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ['user', 'product', 'rating', 'is_anonymous', 'is_approved', 'created_at']
    list_filter = ['rating', 'is_anonymous', 'is_approved', 'created_at']
    search_fields = ['user__username', 'product__name', 'content']
    list_editable = ['is_approved']
    inlines = [ReviewImageInline]
    raw_id_fields = ['user', 'product', 'order']


@admin.register(ReviewImage)
class ReviewImageAdmin(admin.ModelAdmin):
    list_display = ['review', 'image', 'created_at']
    list_filter = ['created_at']
    raw_id_fields = ['review']

#!/usr/bin/env python
"""
创建后台管理员账号
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from admin_panel.models import AdminUser

def create_admin():
    """创建管理员账号"""
    username = 'admin'
    email = '<EMAIL>'
    password = 'admin123'
    
    # 检查是否已存在
    if User.objects.filter(username=username).exists():
        print(f"管理员账号 {username} 已存在")
        return
    
    # 创建超级用户
    user = User.objects.create_superuser(
        username=username,
        email=email,
        password=password
    )
    
    # 创建管理员资料
    admin_user = AdminUser.objects.create(
        user=user,
        is_super_admin=True,
        department='技术部',
        position='系统管理员'
    )
    
    print(f"管理员账号创建成功:")
    print(f"用户名: {username}")
    print(f"密码: {password}")
    print(f"邮箱: {email}")
    print(f"登录地址: http://127.0.0.1:8000/admin/login/")

if __name__ == '__main__':
    create_admin()

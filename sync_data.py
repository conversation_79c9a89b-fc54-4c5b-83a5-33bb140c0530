#!/usr/bin/env python
"""
数据同步脚本 - 将前台和后台数据保持一致
"""
import os
import sys
import django
import pymysql

# 配置Django环境
sys.path.append('D:/python/shoping/frontend')
sys.path.append('D:/python/shoping/backend')

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '123456',
    'database': 'shopping',
    'charset': 'utf8mb4'
}

def create_database():
    """创建数据库"""
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            charset=DB_CONFIG['charset']
        )
        
        with connection.cursor() as cursor:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DB_CONFIG['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"数据库 {DB_CONFIG['database']} 创建成功或已存在")
        
        connection.close()
        return True
    except Exception as e:
        print(f"创建数据库失败: {e}")
        return False

def init_sample_data():
    """初始化示例数据"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        
        with connection.cursor() as cursor:
            # 创建分类表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS products_category (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    parent_id INT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    sort_order INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (parent_id) REFERENCES products_category(id) ON DELETE SET NULL
                )
            """)
            
            # 创建品牌表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS products_brand (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    logo VARCHAR(255),
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            """)
            
            # 创建商品表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS products_product (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(200) NOT NULL,
                    description TEXT,
                    price DECIMAL(10,2) NOT NULL,
                    original_price DECIMAL(10,2),
                    stock INT DEFAULT 0,
                    sales INT DEFAULT 0,
                    rating DECIMAL(3,2) DEFAULT 0.0,
                    reviews_count INT DEFAULT 0,
                    is_active BOOLEAN DEFAULT TRUE,
                    is_hot BOOLEAN DEFAULT FALSE,
                    is_new BOOLEAN DEFAULT FALSE,
                    category_id INT,
                    brand_id INT,
                    main_image VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES products_category(id) ON DELETE SET NULL,
                    FOREIGN KEY (brand_id) REFERENCES products_brand(id) ON DELETE SET NULL
                )
            """)
            
            # 插入分类数据
            categories = [
                (1, '电子产品', '各类电子设备', None, True, 1),
                (2, '服装鞋帽', '时尚服饰', None, True, 2),
                (3, '家居用品', '家庭生活用品', None, True, 3),
                (11, '手机通讯', '智能手机、配件', 1, True, 1),
                (12, '电脑办公', '笔记本、台式机', 1, True, 2),
                (13, '数码配件', '耳机、充电器', 1, True, 3),
                (21, '男装', '男士服装', 2, True, 1),
                (22, '女装', '女士服装', 2, True, 2),
                (23, '鞋靴', '各类鞋子', 2, True, 3),
                (31, '家具', '桌椅床柜', 3, True, 1),
                (32, '家纺', '床上用品', 3, True, 2),
                (33, '厨具', '厨房用品', 3, True, 3),
            ]
            
            cursor.execute("DELETE FROM products_category")
            for cat in categories:
                cursor.execute("""
                    INSERT INTO products_category (id, name, description, parent_id, is_active, sort_order)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, cat)
            
            # 插入品牌数据
            brands = [
                (1, '苹果', 'Apple Inc.', '', True),
                (2, '小米', '小米科技', '', True),
                (3, '华为', '华为技术有限公司', '', True),
                (4, '三星', '三星电子', '', True),
                (5, '索尼', '索尼公司', '', True),
                (6, '任天堂', '任天堂株式会社', '', True),
            ]
            
            cursor.execute("DELETE FROM products_brand")
            for brand in brands:
                cursor.execute("""
                    INSERT INTO products_brand (id, name, description, logo, is_active)
                    VALUES (%s, %s, %s, %s, %s)
                """, brand)
            
            # 插入商品数据
            products = [
                (1, '智能手表 Pro', '健康监测、运动追踪、消息提醒', 1299.00, 1599.00, 50, 128, 4.5, 128, True, True, False, 11, 1, 'https://picsum.photos/400/400?random=10'),
                (2, '无线降噪耳机', '主动降噪，高清音质，舒适佩戴', 899.00, 1099.00, 30, 86, 4.0, 86, True, False, True, 13, 2, 'https://picsum.photos/400/400?random=11'),
                (3, '智能音箱', '语音助手，智能家居控制，高品质音响效果', 399.00, 499.00, 25, 64, 4.2, 64, True, True, False, 13, 1, 'https://picsum.photos/400/400?random=12'),
                (4, '运动背包', '专业运动背包，大容量设计，防水耐用', 299.00, 399.00, 40, 45, 4.3, 45, True, False, False, 21, 3, 'https://picsum.photos/400/400?random=13'),
                (5, 'iPhone 15 Pro', '最新款iPhone，A17 Pro芯片，钛金属设计', 8999.00, 9999.00, 10, 256, 4.8, 256, True, True, True, 11, 1, 'https://picsum.photos/400/400?random=14'),
                (6, '小米14 Ultra', '徕卡影像，骁龙8 Gen3，专业摄影手机', 5999.00, 6499.00, 15, 189, 4.6, 189, True, True, True, 11, 2, 'https://picsum.photos/400/400?random=15'),
                (7, 'MacBook Pro 14', 'M3 Pro芯片，14英寸Liquid Retina XDR显示屏', 14999.00, 15999.00, 8, 98, 4.7, 98, True, False, True, 12, 1, 'https://picsum.photos/400/400?random=16'),
                (8, '机械键盘', '青轴机械键盘，RGB背光，游戏办公两用', 599.00, 799.00, 35, 156, 4.4, 156, True, False, False, 12, 3, 'https://picsum.photos/400/400?random=17'),
            ]
            
            cursor.execute("DELETE FROM products_product")
            for product in products:
                cursor.execute("""
                    INSERT INTO products_product (id, name, description, price, original_price, stock, sales, rating, reviews_count, is_active, is_hot, is_new, category_id, brand_id, main_image)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, product)
            
            connection.commit()
            print("示例数据初始化成功")
            
        connection.close()
        return True
    except Exception as e:
        print(f"初始化示例数据失败: {e}")
        return False

def main():
    """主函数"""
    print("开始数据同步...")
    
    # 1. 创建数据库
    if not create_database():
        return
    
    # 2. 初始化示例数据
    if not init_sample_data():
        return
    
    print("数据同步完成！")
    print("\n数据库信息:")
    print(f"- 数据库名: {DB_CONFIG['database']}")
    print(f"- 主机: {DB_CONFIG['host']}")
    print(f"- 用户: {DB_CONFIG['user']}")
    print("\n已创建的表:")
    print("- products_category (商品分类)")
    print("- products_brand (品牌)")
    print("- products_product (商品)")
    print("\n已插入的数据:")
    print("- 12个商品分类")
    print("- 6个品牌")
    print("- 8个商品")

if __name__ == '__main__':
    main()

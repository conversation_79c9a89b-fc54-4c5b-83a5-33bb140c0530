@echo off
echo 启动智能电商系统...
echo.

echo 正在启动前台商城服务器 (端口 8001)...
start "前台商城" cmd /k "cd frontend && python manage.py runserver 127.0.0.1:8001"

echo 等待3秒...
timeout /t 3 /nobreak >nul

echo 正在启动后台管理服务器 (端口 8003)...
start "后台管理" cmd /k "cd backend && python manage.py runserver 127.0.0.1:8003"

echo.
echo 服务器启动完成！
echo.
echo 前台商城地址: http://127.0.0.1:8001/
echo 后台管理地址: http://127.0.0.1:8003/dashboard/
echo.
echo 按任意键退出...
pause >nul

{% extends 'base.html' %}

{% block title %}{{ category.name|default:"商品分类" }} - 智能电商{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- 面包屑导航 -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'products:index' %}" class="text-gray-700 hover:text-orange-primary">
                    <i class="fa fa-home mr-2"></i>首页
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fa fa-angle-right text-gray-400 mx-2"></i>
                    <span class="text-gray-500">电子产品</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- 分类标题 -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">电子产品</h1>
        <p class="text-gray-600">精选优质电子产品，品质保证，价格实惠</p>
    </div>

    <!-- 子分类导航 -->
    <div class="bg-white rounded-xl shadow-sm p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">子分类</h2>
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <a href="{% url 'products:search' %}?category=1" class="group">
                <div class="bg-gray-50 rounded-lg p-4 text-center hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2 group-hover:bg-blue-200 transition-colors">
                        <i class="fa fa-mobile text-blue-600 text-xl"></i>
                    </div>
                    <h3 class="text-sm font-medium text-gray-800">手机通讯</h3>
                </div>
            </a>
            <a href="{% url 'products:search' %}?category=2" class="group">
                <div class="bg-gray-50 rounded-lg p-4 text-center hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2 group-hover:bg-purple-200 transition-colors">
                        <i class="fa fa-laptop text-purple-600 text-xl"></i>
                    </div>
                    <h3 class="text-sm font-medium text-gray-800">电脑办公</h3>
                </div>
            </a>
            <a href="{% url 'products:search' %}?category=3" class="group">
                <div class="bg-gray-50 rounded-lg p-4 text-center hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2 group-hover:bg-green-200 transition-colors">
                        <i class="fa fa-headphones text-green-600 text-xl"></i>
                    </div>
                    <h3 class="text-sm font-medium text-gray-800">数码配件</h3>
                </div>
            </a>
            <a href="{% url 'products:search' %}?category=4" class="group">
                <div class="bg-gray-50 rounded-lg p-4 text-center hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-2 group-hover:bg-red-200 transition-colors">
                        <i class="fa fa-camera text-red-600 text-xl"></i>
                    </div>
                    <h3 class="text-sm font-medium text-gray-800">摄影摄像</h3>
                </div>
            </a>
            <a href="{% url 'products:search' %}?category=5" class="group">
                <div class="bg-gray-50 rounded-lg p-4 text-center hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-2 group-hover:bg-yellow-200 transition-colors">
                        <i class="fa fa-gamepad text-yellow-600 text-xl"></i>
                    </div>
                    <h3 class="text-sm font-medium text-gray-800">游戏设备</h3>
                </div>
            </a>
            <a href="{% url 'products:search' %}?category=6" class="group">
                <div class="bg-gray-50 rounded-lg p-4 text-center hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-2 group-hover:bg-indigo-200 transition-colors">
                        <i class="fa fa-tv text-indigo-600 text-xl"></i>
                    </div>
                    <h3 class="text-sm font-medium text-gray-800">智能家居</h3>
                </div>
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- 筛选侧边栏 -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-xl shadow-sm p-6 sticky top-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">筛选条件</h3>
                
                <!-- 品牌筛选 -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-3">品牌</h4>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">苹果 (Apple)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">小米 (Xiaomi)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">华为 (Huawei)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">三星 (Samsung)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">OPPO</span>
                        </label>
                    </div>
                </div>

                <!-- 价格筛选 -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-3">价格区间</h4>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="price" class="text-orange-primary focus:ring-orange-primary border-gray-300">
                            <span class="ml-2 text-sm text-gray-600">¥100以下</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="price" class="text-orange-primary focus:ring-orange-primary border-gray-300">
                            <span class="ml-2 text-sm text-gray-600">¥100-500</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="price" class="text-orange-primary focus:ring-orange-primary border-gray-300">
                            <span class="ml-2 text-sm text-gray-600">¥500-1000</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="price" class="text-orange-primary focus:ring-orange-primary border-gray-300">
                            <span class="ml-2 text-sm text-gray-600">¥1000-5000</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="price" class="text-orange-primary focus:ring-orange-primary border-gray-300">
                            <span class="ml-2 text-sm text-gray-600">¥5000以上</span>
                        </label>
                    </div>
                </div>

                <!-- 特色筛选 -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-3">商品特色</h4>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">热销商品</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">新品上市</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">促销商品</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">免费配送</span>
                        </label>
                    </div>
                </div>

                <button class="w-full bg-orange-primary hover:bg-orange-secondary text-white py-2 rounded-lg transition-colors">
                    应用筛选
                </button>
            </div>
        </div>

        <!-- 商品列表 -->
        <div class="lg:col-span-3">
            <!-- 排序和视图切换 -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-4">
                    <span class="text-gray-600">共找到 156 件商品</span>
                </div>
                <div class="flex items-center space-x-4">
                    <select class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-primary/30">
                        <option>默认排序</option>
                        <option>价格从低到高</option>
                        <option>价格从高到低</option>
                        <option>销量优先</option>
                        <option>最新上架</option>
                    </select>
                    <div class="flex border border-gray-300 rounded-lg">
                        <button class="px-3 py-2 bg-orange-primary text-white rounded-l-lg">
                            <i class="fa fa-th"></i>
                        </button>
                        <button class="px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-r-lg">
                            <i class="fa fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 商品网格 -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- 商品卡片1 -->
                <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                    <div class="relative">
                        <a href="{% url 'products:detail' 1 %}">
                            <img src="https://picsum.photos/400/400?random=10" alt="智能手表" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                        </a>
                        <div class="absolute top-3 left-3">
                            <span class="bg-orange-primary text-white text-xs font-medium py-1 px-2 rounded">热销</span>
                        </div>
                        <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-heart-o"></i>
                        </button>
                    </div>
                    <div class="p-4">
                        <div class="flex items-center mb-2">
                            <div class="flex text-yellow-400">
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star-half-o"></i>
                            </div>
                            <span class="text-gray-500 text-sm ml-2">4.5 (128)</span>
                        </div>
                        <h3 class="font-medium text-gray-800 mb-1">
                            <a href="{% url 'products:detail' 1 %}" class="hover:text-orange-primary transition-colors">智能手表 Pro</a>
                        </h3>
                        <p class="text-gray-500 text-sm mb-3">健康监测、运动追踪、消息提醒</p>
                        <div class="flex justify-between items-center">
                            <div>
                                <span class="text-orange-primary font-bold">¥1,299</span>
                                <span class="text-gray-400 line-through text-sm ml-2">¥1,599</span>
                            </div>
                            <button class="bg-orange-primary hover:bg-orange-secondary text-white w-10 h-10 rounded-full flex items-center justify-center transition-colors">
                                <i class="fa fa-shopping-cart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 更多商品卡片... -->
                <!-- 这里可以重复上面的商品卡片结构 -->
            </div>

            <!-- 分页 -->
            <div class="flex justify-center mt-8">
                <nav class="flex space-x-2">
                    <button class="px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50" disabled>
                        上一页
                    </button>
                    <button class="px-3 py-2 text-white bg-orange-primary border border-orange-primary rounded-lg">
                        1
                    </button>
                    <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        2
                    </button>
                    <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        3
                    </button>
                    <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        下一页
                    </button>
                </nav>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function applyFilters() {
    // 获取筛选条件并应用
    alert('应用筛选条件');
}

function addToCart(productId) {
    if (!isLoggedIn()) {
        alert('请先登录');
        window.location.href = '{% url "users:login" %}';
        return;
    }
    alert('已添加到购物车');
}

function isLoggedIn() {
    return {% if user.is_authenticated %}true{% else %}false{% endif %};
}
</script>
{% endblock %}

{% extends 'base.html' %}

{% block title %}{{ category.name|default:"商品分类" }} - 智能电商{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- 面包屑导航 -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'products:index' %}" class="text-gray-700 hover:text-orange-primary">
                    <i class="fa fa-home mr-2"></i>首页
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fa fa-angle-right text-gray-400 mx-2"></i>
                    <span class="text-gray-500">电子产品</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- 分类标题 -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ category.name }}</h1>
        <p class="text-gray-600">{{ category.description }}</p>
    </div>

    <!-- 子分类导航 -->
    <div class="bg-white rounded-xl shadow-sm p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">子分类</h2>
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {% for subcategory in subcategories %}
            <a href="{% url 'products:search' %}?category={{ subcategory.id }}" class="group">
                <div class="bg-gray-50 rounded-lg p-4 text-center hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2 group-hover:bg-blue-200 transition-colors">
                        {% if '手机' in subcategory.name %}
                            <i class="fa fa-mobile text-blue-600 text-xl"></i>
                        {% elif '电脑' in subcategory.name %}
                            <i class="fa fa-laptop text-purple-600 text-xl"></i>
                        {% elif '数码' in subcategory.name %}
                            <i class="fa fa-headphones text-green-600 text-xl"></i>
                        {% elif '男装' in subcategory.name %}
                            <i class="fa fa-male text-blue-600 text-xl"></i>
                        {% elif '女装' in subcategory.name %}
                            <i class="fa fa-female text-pink-600 text-xl"></i>
                        {% elif '鞋' in subcategory.name %}
                            <i class="fa fa-shoe-prints text-brown-600 text-xl"></i>
                        {% elif '家具' in subcategory.name %}
                            <i class="fa fa-couch text-orange-600 text-xl"></i>
                        {% elif '家纺' in subcategory.name %}
                            <i class="fa fa-bed text-purple-600 text-xl"></i>
                        {% elif '厨具' in subcategory.name %}
                            <i class="fa fa-utensils text-red-600 text-xl"></i>
                        {% else %}
                            <i class="fa fa-cube text-gray-600 text-xl"></i>
                        {% endif %}
                    </div>
                    <h3 class="text-sm font-medium text-gray-800">{{ subcategory.name }}</h3>
                </div>
            </a>
            {% endfor %}
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- 筛选侧边栏 -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-xl shadow-sm p-6 sticky top-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">筛选条件</h3>
                
                <!-- 品牌筛选 -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-3">品牌</h4>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">苹果 (Apple)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">小米 (Xiaomi)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">华为 (Huawei)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">三星 (Samsung)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">OPPO</span>
                        </label>
                    </div>
                </div>

                <!-- 价格筛选 -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-3">价格区间</h4>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="price" class="text-orange-primary focus:ring-orange-primary border-gray-300">
                            <span class="ml-2 text-sm text-gray-600">¥100以下</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="price" class="text-orange-primary focus:ring-orange-primary border-gray-300">
                            <span class="ml-2 text-sm text-gray-600">¥100-500</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="price" class="text-orange-primary focus:ring-orange-primary border-gray-300">
                            <span class="ml-2 text-sm text-gray-600">¥500-1000</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="price" class="text-orange-primary focus:ring-orange-primary border-gray-300">
                            <span class="ml-2 text-sm text-gray-600">¥1000-5000</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="price" class="text-orange-primary focus:ring-orange-primary border-gray-300">
                            <span class="ml-2 text-sm text-gray-600">¥5000以上</span>
                        </label>
                    </div>
                </div>

                <!-- 特色筛选 -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-3">商品特色</h4>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">热销商品</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">新品上市</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">促销商品</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-orange-primary focus:ring-orange-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">免费配送</span>
                        </label>
                    </div>
                </div>

                <button class="w-full bg-orange-primary hover:bg-orange-secondary text-white py-2 rounded-lg transition-colors">
                    应用筛选
                </button>
            </div>
        </div>

        <!-- 商品列表 -->
        <div class="lg:col-span-3">
            <!-- 排序和视图切换 -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-4">
                    <span class="text-gray-600">共找到 {{ total_count }} 件商品</span>
                </div>
                <div class="flex items-center space-x-4">
                    <select class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-primary/30">
                        <option>默认排序</option>
                        <option>价格从低到高</option>
                        <option>价格从高到低</option>
                        <option>销量优先</option>
                        <option>最新上架</option>
                    </select>
                    <div class="flex border border-gray-300 rounded-lg">
                        <button class="px-3 py-2 bg-orange-primary text-white rounded-l-lg">
                            <i class="fa fa-th"></i>
                        </button>
                        <button class="px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-r-lg">
                            <i class="fa fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 商品网格 -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for product in products %}
                <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                    <div class="relative">
                        <a href="{% url 'products:detail' product.id %}">
                            <img src="{{ product.image }}" alt="{{ product.name }}" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                        </a>
                        {% if product.is_hot %}
                        <div class="absolute top-3 left-3">
                            <span class="bg-orange-primary text-white text-xs font-medium py-1 px-2 rounded">热销</span>
                        </div>
                        {% endif %}
                        {% if product.is_new %}
                        <div class="absolute top-3 {% if product.is_hot %}left-16{% else %}left-3{% endif %}">
                            <span class="bg-green-500 text-white text-xs font-medium py-1 px-2 rounded">新品</span>
                        </div>
                        {% endif %}
                        <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-heart-o"></i>
                        </button>
                    </div>
                    <div class="p-4">
                        <div class="flex items-center mb-2">
                            <div class="flex text-yellow-400">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= product.rating|default:4 %}
                                        <i class="fa fa-star"></i>
                                    {% else %}
                                        <i class="fa fa-star-o"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <span class="text-gray-500 text-sm ml-2">{{ product.rating }} ({{ product.reviews_count }})</span>
                        </div>
                        <h3 class="font-medium text-gray-800 mb-1">
                            <a href="{% url 'products:detail' product.id %}" class="hover:text-orange-primary transition-colors">{{ product.name }}</a>
                        </h3>
                        <p class="text-gray-500 text-sm mb-3">{{ product.description|truncatechars:30 }}</p>
                        <div class="flex justify-between items-center">
                            <div>
                                <span class="text-orange-primary font-bold">¥{{ product.price }}</span>
                                {% if product.original_price and product.original_price > product.price %}
                                <span class="text-gray-400 line-through text-sm ml-2">¥{{ product.original_price }}</span>
                                {% endif %}
                            </div>
                            <button class="bg-orange-primary hover:bg-orange-secondary text-white w-10 h-10 rounded-full flex items-center justify-center transition-colors" onclick="addToCart({{ product.id }})">
                                <i class="fa fa-shopping-cart"></i>
                            </button>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-span-full text-center py-16">
                    <i class="fa fa-shopping-bag text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">暂无商品</h3>
                    <p class="text-gray-500">该分类下暂时没有商品</p>
                </div>
                {% endfor %}
            </div>

            <!-- 分页 -->
            <div class="flex justify-center mt-8">
                <nav class="flex space-x-2">
                    <button class="px-3 py-2 text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50" disabled>
                        上一页
                    </button>
                    <button class="px-3 py-2 text-white bg-orange-primary border border-orange-primary rounded-lg">
                        1
                    </button>
                    <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        2
                    </button>
                    <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        3
                    </button>
                    <button class="px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        下一页
                    </button>
                </nav>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function applyFilters() {
    // 获取筛选条件并应用
    alert('应用筛选条件');
}

function addToCart(productId) {
    if (!isLoggedIn()) {
        alert('请先登录');
        window.location.href = '{% url "users:login" %}';
        return;
    }
    alert('已添加到购物车');
}

function isLoggedIn() {
    return {% if user.is_authenticated %}true{% else %}false{% endif %};
}
</script>
{% endblock %}

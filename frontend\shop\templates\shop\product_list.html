{% extends 'shop/base.html' %}

{% block title %}商品列表 - 智能电商{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="row">
        <!-- 侧边栏筛选 -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-filter me-2"></i>商品筛选</h5>
                </div>
                <div class="card-body">
                    <!-- 分类筛选 -->
                    <div class="mb-3">
                        <h6>商品分类</h6>
                        <div class="list-group list-group-flush">
                            <a href="{% url 'shop:product_list' %}" 
                               class="list-group-item list-group-item-action {% if not current_category %}active{% endif %}">
                                全部分类
                            </a>
                            {% for category in categories %}
                            <a href="{% url 'shop:product_list' %}?category={{ category.id }}" 
                               class="list-group-item list-group-item-action {% if current_category == category.id|stringformat:'s' %}active{% endif %}">
                                {{ category.name }}
                            </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 商品列表 -->
        <div class="col-md-9">
            <!-- 搜索和排序 -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    {% if search %}
                    <h5>搜索结果：{{ search }}</h5>
                    {% else %}
                    <h5>商品列表</h5>
                    {% endif %}
                    <small class="text-muted">共 {{ page_obj.paginator.count }} 件商品</small>
                </div>
                
                <div class="d-flex align-items-center">
                    <label class="me-2">排序：</label>
                    <select class="form-select form-select-sm" id="sortSelect" style="width: auto;">
                        <option value="default" {% if sort == 'default' %}selected{% endif %}>默认</option>
                        <option value="price_asc" {% if sort == 'price_asc' %}selected{% endif %}>价格从低到高</option>
                        <option value="price_desc" {% if sort == 'price_desc' %}selected{% endif %}>价格从高到低</option>
                        <option value="sales" {% if sort == 'sales' %}selected{% endif %}>销量优先</option>
                        <option value="new" {% if sort == 'new' %}selected{% endif %}>最新上架</option>
                    </select>
                </div>
            </div>

            <!-- 商品网格 -->
            <div class="row">
                {% for product in page_obj %}
                <div class="col-md-4 col-lg-3 mb-4">
                    <div class="card product-card h-100">
                        <div class="position-relative">
                            <img src="{{ product.main_image.url }}" class="card-img-top" alt="{{ product.name }}">
                            {% if product.is_hot %}
                            <span class="badge bg-danger position-absolute top-0 start-0 m-2">热门</span>
                            {% endif %}
                            {% if product.is_new %}
                            <span class="badge bg-success position-absolute top-0 end-0 m-2">新品</span>
                            {% endif %}
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">{{ product.name }}</h6>
                            <p class="card-text text-muted small">{{ product.description|truncatechars:40 }}</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <span class="h6 text-primary">¥{{ product.price }}</span>
                                        {% if product.original_price %}
                                        <small class="text-muted text-decoration-line-through">¥{{ product.original_price }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">销量: {{ product.sales }}</small>
                                    <small class="text-muted">库存: {{ product.stock }}</small>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'shop:product_detail' product.id %}" class="btn btn-outline-primary btn-sm">
                                        查看详情
                                    </a>
                                    {% if user.is_authenticated %}
                                    <button class="btn btn-primary btn-sm add-to-cart" data-product-id="{{ product.id }}">
                                        <i class="fas fa-cart-plus"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无商品</h5>
                        <p class="text-muted">请尝试其他搜索条件</p>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- 分页 -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="商品分页">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if sort %}&sort={{ sort }}{% endif %}">
                            上一页
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if sort %}&sort={{ sort }}{% endif %}">
                            {{ num }}
                        </a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if sort %}&sort={{ sort }}{% endif %}">
                            下一页
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 排序选择
    $('#sortSelect').change(function() {
        var sort = $(this).val();
        var url = new URL(window.location);
        if (sort === 'default') {
            url.searchParams.delete('sort');
        } else {
            url.searchParams.set('sort', sort);
        }
        window.location.href = url.toString();
    });

    // 添加到购物车
    $('.add-to-cart').click(function() {
        var productId = $(this).data('product-id');
        var button = $(this);
        
        $.ajax({
            url: '{% url "shop:add_to_cart" %}',
            type: 'POST',
            data: JSON.stringify({
                'product_id': productId,
                'quantity': 1
            }),
            contentType: 'application/json',
            headers: {
                'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.success) {
                    button.html('<i class="fas fa-check"></i>');
                    button.removeClass('btn-primary').addClass('btn-success');
                    setTimeout(function() {
                        button.html('<i class="fas fa-cart-plus"></i>');
                        button.removeClass('btn-success').addClass('btn-primary');
                    }, 2000);
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('添加失败，请重试');
            }
        });
    });
});
</script>
{% endblock %}

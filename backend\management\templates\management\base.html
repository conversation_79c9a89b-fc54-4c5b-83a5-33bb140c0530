<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}智能电商管理后台{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FF8C42;
            --accent-color: #FFA500;
            --text-color: #333;
            --bg-color: #f8f9fa;
            --sidebar-bg: #343a40;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            color: var(--text-color);
            background-color: var(--bg-color);
        }
        
        .sidebar {
            background-color: var(--sidebar-bg);
            min-height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
        }
        
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 0.75rem 1rem;
            border-radius: 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: var(--primary-color);
        }
        
        .sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }
        
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: bold;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .text-primary {
            color: var(--primary-color) !important;
        }
        
        .bg-primary {
            background-color: var(--primary-color) !important;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }
        
        .table th {
            background-color: var(--bg-color);
            border-top: none;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                margin-left: -250px;
            }
            
            .sidebar.show {
                margin-left: 0;
            }
            
            .main-content {
                margin-left: 0;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar">
        <div class="p-3">
            <a href="{% url 'management:dashboard' %}" class="navbar-brand text-decoration-none">
                <i class="fas fa-cogs me-2"></i>智能电商管理
            </a>
        </div>
        
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{% url 'management:dashboard' %}">
                    <i class="fas fa-tachometer-alt"></i>仪表盘
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'management:user_list' %}">
                    <i class="fas fa-users"></i>用户管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'management:product_list' %}">
                    <i class="fas fa-box"></i>商品管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'management:category_list' %}">
                    <i class="fas fa-tags"></i>分类管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'management:order_list' %}">
                    <i class="fas fa-shopping-cart"></i>订单管理
                </a>
            </li>
        </ul>
        
        <div class="mt-auto p-3">
            <div class="dropdown">
                <a class="nav-link dropdown-toggle text-light" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user"></i> {{ user.username }}
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{% url 'management:logout' %}">退出登录</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="main-content">
        <!-- 顶部导航 -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white mb-4">
            <div class="container-fluid">
                <button class="navbar-toggler d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="http://127.0.0.1:8001/" target="_blank">
                        <i class="fas fa-external-link-alt"></i> 前台商城
                    </a>
                </div>
            </div>
        </nav>

        <!-- 消息提示 -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        <!-- 页面内容 -->
        {% block content %}{% endblock %}
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>

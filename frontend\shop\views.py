from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Avg
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
import json
import uuid

from .models import (
    Product, Category, Cart, Order, OrderItem, Address, 
    Favorite, Review, ReviewImage, Banner, UserProfile
)


def index(request):
    """首页"""
    # 轮播图
    banners = Banner.objects.filter(is_active=True)[:5]
    
    # 热门商品
    hot_products = Product.objects.filter(is_hot=True, is_active=True)[:6]
    
    # 新品上市
    new_products = Product.objects.filter(is_new=True, is_active=True)[:8]
    
    # 主分类
    main_categories = Category.objects.filter(parent=None, is_active=True)[:10]
    
    context = {
        'banners': banners,
        'hot_products': hot_products,
        'new_products': new_products,
        'main_categories': main_categories,
    }
    return render(request, 'shop/index.html', context)


def product_list(request):
    """商品列表"""
    products = Product.objects.filter(is_active=True)
    categories = Category.objects.filter(is_active=True)
    
    # 分类筛选
    category_id = request.GET.get('category')
    if category_id:
        category = get_object_or_404(Category, id=category_id)
        if category.parent:
            products = products.filter(category=category)
        else:
            # 如果是主分类，显示所有子分类的商品
            child_categories = category.category_set.all()
            products = products.filter(category__in=child_categories)
    
    # 搜索
    search = request.GET.get('search')
    if search:
        products = products.filter(name__icontains=search)
    
    # 排序
    sort = request.GET.get('sort', 'default')
    if sort == 'price_asc':
        products = products.order_by('price')
    elif sort == 'price_desc':
        products = products.order_by('-price')
    elif sort == 'sales':
        products = products.order_by('-sales')
    elif sort == 'new':
        products = products.order_by('-created_at')
    
    # 分页
    paginator = Paginator(products, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'categories': categories,
        'current_category': category_id,
        'search': search,
        'sort': sort,
    }
    return render(request, 'shop/product_list.html', context)


def product_detail(request, product_id):
    """商品详情"""
    product = get_object_or_404(Product, id=product_id, is_active=True)
    
    # 商品图片
    product_images = product.productimage_set.all()
    
    # 相关商品
    related_products = Product.objects.filter(
        category=product.category, 
        is_active=True
    ).exclude(id=product.id)[:4]
    
    # 商品评价
    reviews = product.review_set.all()[:10]
    
    # 用户是否收藏
    is_favorited = False
    if request.user.is_authenticated:
        is_favorited = Favorite.objects.filter(
            user=request.user, 
            product=product
        ).exists()
    
    context = {
        'product': product,
        'product_images': product_images,
        'related_products': related_products,
        'reviews': reviews,
        'is_favorited': is_favorited,
    }
    return render(request, 'shop/product_detail.html', context)


def user_register(request):
    """用户注册"""
    if request.method == 'POST':
        username = request.POST.get('username')
        email = request.POST.get('email')
        password = request.POST.get('password')
        confirm_password = request.POST.get('confirm_password')
        
        # 验证
        if not all([username, email, password, confirm_password]):
            messages.error(request, '请填写完整信息')
            return render(request, 'shop/register.html')
        
        if password != confirm_password:
            messages.error(request, '两次密码不一致')
            return render(request, 'shop/register.html')
        
        if User.objects.filter(username=username).exists():
            messages.error(request, '用户名已存在')
            return render(request, 'shop/register.html')
        
        if User.objects.filter(email=email).exists():
            messages.error(request, '邮箱已被注册')
            return render(request, 'shop/register.html')
        
        # 创建用户
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password
        )
        
        # 创建用户资料
        UserProfile.objects.create(user=user)
        
        messages.success(request, '注册成功，请登录')
        return redirect('shop:login')
    
    return render(request, 'shop/register.html')


def user_login(request):
    """用户登录"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        
        user = authenticate(request, username=username, password=password)
        if user:
            login(request, user)
            next_url = request.GET.get('next', 'shop:index')
            return redirect(next_url)
        else:
            messages.error(request, '用户名或密码错误')
    
    return render(request, 'shop/login.html')


def user_logout(request):
    """用户退出"""
    logout(request)
    return redirect('shop:index')


@login_required
def user_profile(request):
    """个人中心"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)
    
    if request.method == 'POST':
        # 更新用户信息
        request.user.first_name = request.POST.get('first_name', '')
        request.user.last_name = request.POST.get('last_name', '')
        request.user.email = request.POST.get('email', '')
        request.user.save()
        
        # 更新用户资料
        profile.phone = request.POST.get('phone', '')
        profile.gender = request.POST.get('gender', '')
        if request.POST.get('birthday'):
            profile.birthday = request.POST.get('birthday')
        
        if 'avatar' in request.FILES:
            profile.avatar = request.FILES['avatar']
        
        profile.save()
        messages.success(request, '资料更新成功')
    
    context = {
        'profile': profile,
    }
    return render(request, 'shop/profile.html', context)


@login_required
def cart_view(request):
    """购物车"""
    cart_items = Cart.objects.filter(user=request.user)
    
    total_amount = sum(item.total_price for item in cart_items)
    
    context = {
        'cart_items': cart_items,
        'total_amount': total_amount,
    }
    return render(request, 'shop/cart.html', context)


@login_required
@csrf_exempt
def add_to_cart(request):
    """添加到购物车"""
    if request.method == 'POST':
        data = json.loads(request.body)
        product_id = data.get('product_id')
        quantity = int(data.get('quantity', 1))
        
        product = get_object_or_404(Product, id=product_id)
        
        # 检查库存
        if product.stock < quantity:
            return JsonResponse({'success': False, 'message': '库存不足'})
        
        cart_item, created = Cart.objects.get_or_create(
            user=request.user,
            product=product,
            defaults={'quantity': quantity}
        )
        
        if not created:
            cart_item.quantity += quantity
            if cart_item.quantity > product.stock:
                return JsonResponse({'success': False, 'message': '库存不足'})
            cart_item.save()
        
        return JsonResponse({'success': True, 'message': '添加成功'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
def order_list(request):
    """订单列表"""
    orders = Order.objects.filter(user=request.user)
    
    # 状态筛选
    status = request.GET.get('status')
    if status:
        orders = orders.filter(status=status)
    
    paginator = Paginator(orders, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'current_status': status,
    }
    return render(request, 'shop/order_list.html', context)


@login_required
def order_detail(request, order_id):
    """订单详情"""
    order = get_object_or_404(Order, id=order_id, user=request.user)
    order_items = order.orderitem_set.all()
    
    context = {
        'order': order,
        'order_items': order_items,
    }
    return render(request, 'shop/order_detail.html', context)


@login_required
def create_order(request):
    """创建订单"""
    if request.method == 'POST':
        # 获取购物车商品
        cart_items = Cart.objects.filter(user=request.user)
        if not cart_items:
            messages.error(request, '购物车为空')
            return redirect('shop:cart')
        
        # 检查库存
        for item in cart_items:
            if item.product.stock < item.quantity:
                messages.error(request, f'商品 {item.product.name} 库存不足')
                return redirect('shop:cart')
        
        # 计算金额
        total_amount = sum(item.total_price for item in cart_items)
        
        # 优惠计算
        discount_amount = 0
        if total_amount >= 1000:
            discount_amount = 200
        elif total_amount >= 300:
            discount_amount = 50
        elif total_amount >= 100:
            discount_amount = 10
        
        final_amount = total_amount - discount_amount
        
        # 创建订单
        order = Order.objects.create(
            order_no=str(uuid.uuid4()).replace('-', ''),
            user=request.user,
            total_amount=total_amount,
            discount_amount=discount_amount,
            final_amount=final_amount,
            receiver_name=request.POST.get('receiver_name'),
            receiver_phone=request.POST.get('receiver_phone'),
            receiver_address=request.POST.get('receiver_address'),
            note=request.POST.get('note', ''),
        )
        
        # 创建订单商品
        for item in cart_items:
            OrderItem.objects.create(
                order=order,
                product=item.product,
                quantity=item.quantity,
                price=item.product.price,
                total_price=item.total_price,
            )
            
            # 减少库存
            item.product.stock -= item.quantity
            item.product.sales += item.quantity
            item.product.save()
        
        # 清空购物车
        cart_items.delete()
        
        messages.success(request, '订单创建成功')
        return redirect('shop:order_detail', order_id=order.id)
    
    # 获取收货地址
    addresses = Address.objects.filter(user=request.user)
    cart_items = Cart.objects.filter(user=request.user)
    total_amount = sum(item.total_price for item in cart_items)
    
    context = {
        'addresses': addresses,
        'cart_items': cart_items,
        'total_amount': total_amount,
    }
    return render(request, 'shop/create_order.html', context)

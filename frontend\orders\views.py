from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import json

from .models import Order, OrderItem, OrderManager
from users.models import Address


@login_required
def create_order(request):
    """创建订单"""
    if request.method == 'POST':
        address_id = request.POST.get('address_id')
        note = request.POST.get('note', '')
        
        # 获取收货地址
        try:
            address = Address.objects.get(id=address_id, user=request.user)
            address_data = {
                'receiver_name': address.name,
                'receiver_phone': address.phone,
                'receiver_province': address.province,
                'receiver_city': address.city,
                'receiver_district': address.district,
                'receiver_address': address.address,
                'receiver_postal_code': address.postal_code,
            }
        except Address.DoesNotExist:
            messages.error(request, '请选择收货地址')
            return redirect('cart:checkout')
        
        # 创建订单
        order, message = OrderManager.create_order_from_cart(request.user, address_data, note)
        
        if order:
            messages.success(request, message)
            return redirect('orders:detail', order_id=order.id)
        else:
            messages.error(request, message)
            return redirect('cart:checkout')
    
    return redirect('cart:checkout')


@login_required
def order_list(request):
    """订单列表"""
    orders = Order.objects.filter(user=request.user)
    
    # 状态筛选
    status = request.GET.get('status')
    if status:
        orders = orders.filter(status=status)
    
    # 分页
    paginator = Paginator(orders, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'current_status': status,
        'status_choices': Order.STATUS_CHOICES,
    }
    return render(request, 'orders/order_list.html', context)


@login_required
def order_detail(request, order_id):
    """订单详情"""
    order = get_object_or_404(Order, id=order_id, user=request.user)
    order_items = order.items.select_related('product').all()
    
    context = {
        'order': order,
        'order_items': order_items,
    }
    return render(request, 'orders/order_detail.html', context)


@login_required
@csrf_exempt
def cancel_order(request):
    """取消订单"""
    if request.method == 'POST':
        data = json.loads(request.body)
        order_id = data.get('order_id')
        reason = data.get('reason', '')
        
        try:
            order = Order.objects.get(id=order_id, user=request.user)
            
            if order.cancel(reason):
                return JsonResponse({'success': True, 'message': '订单取消成功'})
            else:
                return JsonResponse({'success': False, 'message': '订单状态不允许取消'})
                
        except Order.DoesNotExist:
            return JsonResponse({'success': False, 'message': '订单不存在'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
@csrf_exempt
def confirm_receipt(request):
    """确认收货"""
    if request.method == 'POST':
        data = json.loads(request.body)
        order_id = data.get('order_id')
        
        try:
            order = Order.objects.get(id=order_id, user=request.user)
            
            if order.confirm_receipt():
                return JsonResponse({'success': True, 'message': '确认收货成功'})
            else:
                return JsonResponse({'success': False, 'message': '订单状态不允许确认收货'})
                
        except Order.DoesNotExist:
            return JsonResponse({'success': False, 'message': '订单不存在'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
def order_tracking(request, order_id):
    """订单物流跟踪"""
    order = get_object_or_404(Order, id=order_id, user=request.user)
    
    # 这里可以集成第三方物流API获取物流信息
    tracking_info = []
    
    context = {
        'order': order,
        'tracking_info': tracking_info,
    }
    return render(request, 'orders/tracking.html', context)

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import json

from .models import Order, OrderItem, OrderManager
from users.models import Address


@login_required
def create_order(request):
    """创建订单"""
    if request.method == 'POST':
        address_id = request.POST.get('address_id')
        note = request.POST.get('note', '')
        
        # 获取收货地址
        try:
            address = Address.objects.get(id=address_id, user=request.user)
            address_data = {
                'receiver_name': address.name,
                'receiver_phone': address.phone,
                'receiver_province': address.province,
                'receiver_city': address.city,
                'receiver_district': address.district,
                'receiver_address': address.address,
                'receiver_postal_code': address.postal_code,
            }
        except Address.DoesNotExist:
            messages.error(request, '请选择收货地址')
            return redirect('cart:checkout')
        
        # 创建订单
        order, message = OrderManager.create_order_from_cart(request.user, address_data, note)
        
        if order:
            messages.success(request, message)
            return redirect('orders:detail', order_id=order.id)
        else:
            messages.error(request, message)
            return redirect('cart:checkout')
    
    return redirect('cart:checkout')


@login_required
def order_list(request):
    """订单列表"""
    # 使用静态数据模拟订单
    all_orders = [
        {
            'id': 'ORD202412010001',
            'order_number': 'ORD202412010001',
            'status': 'pending',
            'status_display': '待付款',
            'total_amount': 3497.00,
            'created_at': '2024-12-01 10:30:00',
            'items': [
                {
                    'product': {
                        'name': '智能手表 Pro',
                        'image': 'https://picsum.photos/400/400?random=10'
                    },
                    'quantity': 2,
                    'price': 1299.00
                },
                {
                    'product': {
                        'name': '无线降噪耳机',
                        'image': 'https://picsum.photos/400/400?random=11'
                    },
                    'quantity': 1,
                    'price': 899.00
                }
            ]
        },
        {
            'id': 'ORD202411280002',
            'order_number': 'ORD202411280002',
            'status': 'shipped',
            'status_display': '已发货',
            'total_amount': 8999.00,
            'created_at': '2024-11-28 15:20:00',
            'items': [
                {
                    'product': {
                        'name': 'iPhone 15 Pro',
                        'image': 'https://picsum.photos/400/400?random=14'
                    },
                    'quantity': 1,
                    'price': 8999.00
                }
            ]
        },
        {
            'id': 'ORD202411250003',
            'order_number': 'ORD202411250003',
            'status': 'completed',
            'status_display': '已完成',
            'total_amount': 14999.00,
            'created_at': '2024-11-25 09:15:00',
            'items': [
                {
                    'product': {
                        'name': 'MacBook Pro 14',
                        'image': 'https://picsum.photos/400/400?random=16'
                    },
                    'quantity': 1,
                    'price': 14999.00
                }
            ]
        },
        {
            'id': 'ORD202411200004',
            'order_number': 'ORD202411200004',
            'status': 'cancelled',
            'status_display': '已取消',
            'total_amount': 599.00,
            'created_at': '2024-11-20 14:45:00',
            'items': [
                {
                    'product': {
                        'name': '机械键盘',
                        'image': 'https://picsum.photos/400/400?random=17'
                    },
                    'quantity': 1,
                    'price': 599.00
                }
            ]
        }
    ]

    # 状态筛选
    status = request.GET.get('status')
    if status:
        orders = [order for order in all_orders if order['status'] == status]
    else:
        orders = all_orders

    # 状态选项
    status_choices = [
        ('pending', '待付款'),
        ('paid', '已付款'),
        ('shipped', '已发货'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
    ]

    context = {
        'orders': orders,
        'current_status': status,
        'status_choices': status_choices,
        'total_orders': len(orders)
    }
    return render(request, 'orders/order_list.html', context)


@login_required
def order_detail(request, order_id):
    """订单详情"""
    order = get_object_or_404(Order, id=order_id, user=request.user)
    order_items = order.items.select_related('product').all()
    
    context = {
        'order': order,
        'order_items': order_items,
    }
    return render(request, 'orders/order_detail.html', context)


@login_required
@csrf_exempt
def cancel_order(request):
    """取消订单"""
    if request.method == 'POST':
        data = json.loads(request.body)
        order_id = data.get('order_id')
        reason = data.get('reason', '')
        
        try:
            order = Order.objects.get(id=order_id, user=request.user)
            
            if order.cancel(reason):
                return JsonResponse({'success': True, 'message': '订单取消成功'})
            else:
                return JsonResponse({'success': False, 'message': '订单状态不允许取消'})
                
        except Order.DoesNotExist:
            return JsonResponse({'success': False, 'message': '订单不存在'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
@csrf_exempt
def confirm_receipt(request):
    """确认收货"""
    if request.method == 'POST':
        data = json.loads(request.body)
        order_id = data.get('order_id')
        
        try:
            order = Order.objects.get(id=order_id, user=request.user)
            
            if order.confirm_receipt():
                return JsonResponse({'success': True, 'message': '确认收货成功'})
            else:
                return JsonResponse({'success': False, 'message': '订单状态不允许确认收货'})
                
        except Order.DoesNotExist:
            return JsonResponse({'success': False, 'message': '订单不存在'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})


@login_required
def order_tracking(request, order_id):
    """订单物流跟踪"""
    order = get_object_or_404(Order, id=order_id, user=request.user)
    
    # 这里可以集成第三方物流API获取物流信息
    tracking_info = []
    
    context = {
        'order': order,
        'tracking_info': tracking_info,
    }
    return render(request, 'orders/tracking.html', context)

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from .models import Review, ReviewImage


@login_required
def add_review(request, order_id, product_id):
    """添加评价"""
    from orders.models import Order
    from products.models import Product
    
    order = get_object_or_404(Order, id=order_id, user=request.user)
    product = get_object_or_404(Product, id=product_id)
    
    # 检查是否可以评价
    if not order.can_review:
        messages.error(request, '订单状态不允许评价')
        return redirect('orders:detail', order_id=order.id)
    
    # 检查是否已评价
    if Review.objects.filter(user=request.user, product=product, order=order).exists():
        messages.error(request, '您已经评价过该商品')
        return redirect('orders:detail', order_id=order.id)
    
    if request.method == 'POST':
        rating = int(request.POST.get('rating'))
        content = request.POST.get('content')
        is_anonymous = request.POST.get('is_anonymous') == 'on'
        
        # 创建评价
        review = Review.objects.create(
            user=request.user,
            product=product,
            order=order,
            rating=rating,
            content=content,
            is_anonymous=is_anonymous
        )
        
        # 处理评价图片
        for i in range(1, 6):  # 最多5张图片
            image_key = f'image_{i}'
            if image_key in request.FILES:
                ReviewImage.objects.create(
                    review=review,
                    image=request.FILES[image_key]
                )
        
        messages.success(request, '评价提交成功')
        return redirect('orders:detail', order_id=order.id)
    
    context = {
        'order': order,
        'product': product,
    }
    return render(request, 'reviews/add_review.html', context)


@login_required
def my_reviews(request):
    """我的评价"""
    reviews = Review.objects.filter(user=request.user).select_related('product', 'order')
    
    # 分页
    paginator = Paginator(reviews, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
    }
    return render(request, 'reviews/my_reviews.html', context)


def product_reviews(request, product_id):
    """商品评价列表"""
    from products.models import Product
    
    product = get_object_or_404(Product, id=product_id)
    reviews = Review.objects.filter(product=product, is_approved=True).select_related('user')
    
    # 评分筛选
    rating_filter = request.GET.get('rating')
    if rating_filter:
        reviews = reviews.filter(rating=rating_filter)
    
    # 分页
    paginator = Paginator(reviews, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # 评分统计
    rating_stats = {}
    for i in range(1, 6):
        rating_stats[i] = Review.objects.filter(product=product, rating=i, is_approved=True).count()
    
    context = {
        'product': product,
        'page_obj': page_obj,
        'rating_stats': rating_stats,
        'current_rating': rating_filter,
    }
    return render(request, 'reviews/product_reviews.html', context)

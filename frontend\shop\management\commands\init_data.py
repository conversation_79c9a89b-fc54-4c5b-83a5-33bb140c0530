from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from shop.models import Category, Product, Banner, UserProfile
from decimal import Decimal
import os


class Command(BaseCommand):
    help = '初始化示例数据'

    def handle(self, *args, **options):
        self.stdout.write('开始初始化示例数据...')
        
        # 创建商品分类
        self.create_categories()
        
        # 创建示例商品
        self.create_products()
        
        # 创建轮播图
        self.create_banners()
        
        # 创建测试用户
        self.create_test_users()
        
        self.stdout.write(self.style.SUCCESS('示例数据初始化完成！'))

    def create_categories(self):
        """创建商品分类"""
        categories_data = [
            {'name': '电子产品', 'sort_order': 1},
            {'name': '服装鞋帽', 'sort_order': 2},
            {'name': '家居用品', 'sort_order': 3},
            {'name': '图书音像', 'sort_order': 4},
            {'name': '运动户外', 'sort_order': 5},
            {'name': '美妆护肤', 'sort_order': 6},
            {'name': '食品饮料', 'sort_order': 7},
            {'name': '母婴用品', 'sort_order': 8},
            {'name': '汽车用品', 'sort_order': 9},
            {'name': '办公用品', 'sort_order': 10},
        ]
        
        for data in categories_data:
            category, created = Category.objects.get_or_create(
                name=data['name'],
                defaults={
                    'sort_order': data['sort_order'],
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f'创建分类: {category.name}')

    def create_products(self):
        """创建示例商品"""
        # 获取分类
        electronics = Category.objects.get(name='电子产品')
        clothing = Category.objects.get(name='服装鞋帽')
        home = Category.objects.get(name='家居用品')
        books = Category.objects.get(name='图书音像')
        
        products_data = [
            # 电子产品
            {
                'name': 'iPhone 15 Pro Max',
                'category': electronics,
                'description': '苹果最新旗舰手机，搭载A17 Pro芯片，钛金属机身，专业级摄影系统。',
                'price': Decimal('9999.00'),
                'original_price': Decimal('10999.00'),
                'stock': 50,
                'is_hot': True,
                'is_new': True,
            },
            {
                'name': '小米14 Ultra',
                'category': electronics,
                'description': '小米年度旗舰，徕卡影像，骁龙8 Gen3处理器，120W快充。',
                'price': Decimal('5999.00'),
                'original_price': Decimal('6499.00'),
                'stock': 30,
                'is_hot': True,
            },
            {
                'name': 'MacBook Pro 16英寸',
                'category': electronics,
                'description': 'M3 Max芯片，36GB内存，1TB存储，专业创作利器。',
                'price': Decimal('25999.00'),
                'stock': 20,
                'is_new': True,
            },
            
            # 服装鞋帽
            {
                'name': '优衣库羽绒服',
                'category': clothing,
                'description': '轻薄保暖，时尚百搭，多色可选，冬季必备单品。',
                'price': Decimal('399.00'),
                'original_price': Decimal('599.00'),
                'stock': 100,
                'is_hot': True,
            },
            {
                'name': 'Nike Air Max 270',
                'category': clothing,
                'description': '经典气垫跑鞋，舒适透气，运动时尚，多种配色。',
                'price': Decimal('899.00'),
                'stock': 80,
            },
            
            # 家居用品
            {
                'name': '戴森V15无线吸尘器',
                'category': home,
                'description': '强劲吸力，激光显尘，智能感应，家庭清洁好帮手。',
                'price': Decimal('3990.00'),
                'original_price': Decimal('4490.00'),
                'stock': 25,
                'is_hot': True,
            },
            {
                'name': '小米空气净化器Pro',
                'category': home,
                'description': '高效过滤，智能控制，静音运行，呵护家人健康。',
                'price': Decimal('1299.00'),
                'stock': 60,
                'is_new': True,
            },
            
            # 图书音像
            {
                'name': '《人工智能简史》',
                'category': books,
                'description': '深入浅出讲解AI发展历程，适合科技爱好者阅读。',
                'price': Decimal('59.00'),
                'original_price': Decimal('79.00'),
                'stock': 200,
            },
        ]
        
        for data in products_data:
            # 这里应该有实际的图片文件，暂时跳过图片
            product_data = data.copy()
            # 移除图片字段，因为我们没有实际的图片文件
            
            product, created = Product.objects.get_or_create(
                name=product_data['name'],
                defaults=product_data
            )
            if created:
                self.stdout.write(f'创建商品: {product.name}')

    def create_banners(self):
        """创建轮播图"""
        banners_data = [
            {
                'title': '新年大促销',
                'sort_order': 1,
            },
            {
                'title': '春季新品上市',
                'sort_order': 2,
            },
            {
                'title': '电子产品专场',
                'sort_order': 3,
            },
        ]
        
        for data in banners_data:
            banner, created = Banner.objects.get_or_create(
                title=data['title'],
                defaults={
                    'sort_order': data['sort_order'],
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f'创建轮播图: {banner.title}')

    def create_test_users(self):
        """创建测试用户"""
        # 创建普通测试用户
        test_user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'first_name': '测试',
                'last_name': '用户',
            }
        )
        if created:
            test_user.set_password('test123456')
            test_user.save()
            
            # 创建用户资料
            UserProfile.objects.create(
                user=test_user,
                phone='13800138000',
                gender='male',
                is_vip=False,
                balance=Decimal('1000.00')
            )
            self.stdout.write(f'创建测试用户: {test_user.username}')
        
        # 创建VIP测试用户
        vip_user, created = User.objects.get_or_create(
            username='vipuser',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'VIP',
                'last_name': '用户',
            }
        )
        if created:
            vip_user.set_password('vip123456')
            vip_user.save()
            
            # 创建VIP用户资料
            UserProfile.objects.create(
                user=vip_user,
                phone='13900139000',
                gender='female',
                is_vip=True,
                balance=Decimal('5000.00')
            )
            self.stdout.write(f'创建VIP用户: {vip_user.username}')

# Generated by Django 4.2.22 on 2025-06-10 09:53

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='OperationLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('create', '创建'), ('update', '更新'), ('delete', '删除'), ('login', '登录'), ('logout', '退出')], max_length=20, verbose_name='操作类型')),
                ('target_model', models.CharField(blank=True, max_length=50, null=True, verbose_name='目标模型')),
                ('target_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='目标ID')),
                ('description', models.TextField(verbose_name='操作描述')),
                ('ip_address', models.GenericIPAddressField(verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='用户代理')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='操作时间')),
                ('admin_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='操作人')),
            ],
            options={
                'verbose_name': '操作日志',
                'verbose_name_plural': '操作日志',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AdminUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone', models.CharField(blank=True, max_length=11, null=True, verbose_name='手机号')),
                ('department', models.CharField(blank=True, max_length=50, null=True, verbose_name='部门')),
                ('position', models.CharField(blank=True, max_length=50, null=True, verbose_name='职位')),
                ('is_super_admin', models.BooleanField(default=False, verbose_name='超级管理员')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '管理员用户',
                'verbose_name_plural': '管理员用户',
            },
        ),
    ]

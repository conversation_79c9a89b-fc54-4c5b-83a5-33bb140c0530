from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from products.models import Product
import uuid
from datetime import datetime


class DiscountRule(models.Model):
    """优惠规则"""
    name = models.CharField(max_length=100, verbose_name='规则名称')
    min_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='最低消费金额')
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='优惠金额')
    discount_percentage = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        null=True, 
        blank=True, 
        verbose_name='优惠百分比'
    )
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    start_time = models.DateTimeField(null=True, blank=True, verbose_name='开始时间')
    end_time = models.DateTimeField(null=True, blank=True, verbose_name='结束时间')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '优惠规则'
        verbose_name_plural = '优惠规则'
        ordering = ['-min_amount']

    def __str__(self):
        return self.name

    def calculate_discount(self, amount):
        """计算优惠金额"""
        if amount < self.min_amount:
            return 0
        
        if self.discount_percentage:
            return amount * (self.discount_percentage / 100)
        else:
            return self.discount_amount

    @classmethod
    def get_best_discount(cls, amount):
        """获取最优优惠"""
        rules = cls.objects.filter(
            is_active=True,
            min_amount__lte=amount
        ).order_by('-discount_amount')
        
        if rules.exists():
            return rules.first()
        return None


class Order(models.Model):
    """订单"""
    STATUS_CHOICES = [
        ('pending', '待支付'),
        ('paid', '已支付'),
        ('shipped', '已发货'),
        ('delivered', '已收货'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
        ('refunded', '已退款'),
    ]

    order_no = models.CharField(max_length=50, unique=True, verbose_name='订单号')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='订单状态')
    
    # 金额信息
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='商品总额')
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='优惠金额')
    shipping_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='运费')
    final_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='实付金额')
    
    # 收货信息
    receiver_name = models.CharField(max_length=50, verbose_name='收货人')
    receiver_phone = models.CharField(max_length=11, verbose_name='收货电话')
    receiver_province = models.CharField(max_length=50, verbose_name='收货省份')
    receiver_city = models.CharField(max_length=50, verbose_name='收货城市')
    receiver_district = models.CharField(max_length=50, verbose_name='收货区县')
    receiver_address = models.CharField(max_length=200, verbose_name='收货地址')
    receiver_postal_code = models.CharField(max_length=6, null=True, blank=True, verbose_name='邮政编码')
    
    # 订单信息
    note = models.TextField(null=True, blank=True, verbose_name='订单备注')
    discount_rule = models.ForeignKey(
        DiscountRule, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        verbose_name='使用的优惠规则'
    )
    
    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    paid_at = models.DateTimeField(null=True, blank=True, verbose_name='支付时间')
    shipped_at = models.DateTimeField(null=True, blank=True, verbose_name='发货时间')
    delivered_at = models.DateTimeField(null=True, blank=True, verbose_name='收货时间')
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name='完成时间')
    cancelled_at = models.DateTimeField(null=True, blank=True, verbose_name='取消时间')
    
    # 物流信息
    shipping_company = models.CharField(max_length=50, null=True, blank=True, verbose_name='物流公司')
    tracking_number = models.CharField(max_length=50, null=True, blank=True, verbose_name='物流单号')

    class Meta:
        verbose_name = '订单'
        verbose_name_plural = '订单'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['order_no']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return self.order_no

    def save(self, *args, **kwargs):
        if not self.order_no:
            self.order_no = self.generate_order_no()
        super().save(*args, **kwargs)

    @staticmethod
    def generate_order_no():
        """生成订单号"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        random_str = str(uuid.uuid4()).replace('-', '')[:6].upper()
        return f"ORD{timestamp}{random_str}"

    @property
    def full_address(self):
        """完整收货地址"""
        return f"{self.receiver_province}{self.receiver_city}{self.receiver_district}{self.receiver_address}"

    @property
    def can_cancel(self):
        """是否可以取消"""
        return self.status in ['pending', 'paid']

    @property
    def can_confirm_receipt(self):
        """是否可以确认收货"""
        return self.status == 'shipped'

    @property
    def can_review(self):
        """是否可以评价"""
        return self.status in ['delivered', 'completed']

    def cancel(self, reason=''):
        """取消订单"""
        if self.can_cancel:
            self.status = 'cancelled'
            self.cancelled_at = datetime.now()
            self.save()
            
            # 恢复库存
            for item in self.items.all():
                item.product.stock += item.quantity
                item.product.sales -= item.quantity
                item.product.save()
            
            return True
        return False

    def confirm_receipt(self):
        """确认收货"""
        if self.can_confirm_receipt:
            self.status = 'delivered'
            self.delivered_at = datetime.now()
            self.save()
            return True
        return False

    def complete(self):
        """完成订单"""
        if self.status == 'delivered':
            self.status = 'completed'
            self.completed_at = datetime.now()
            self.save()
            return True
        return False


class OrderItem(models.Model):
    """订单商品项"""
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items', verbose_name='订单')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name='商品')
    product_name = models.CharField(max_length=200, verbose_name='商品名称')
    product_sku = models.CharField(max_length=50, verbose_name='商品编码')
    product_image = models.CharField(max_length=500, verbose_name='商品图片')
    quantity = models.IntegerField(validators=[MinValueValidator(1)], verbose_name='数量')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='单价')
    total_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='小计')

    class Meta:
        verbose_name = '订单商品项'
        verbose_name_plural = '订单商品项'

    def __str__(self):
        return f"{self.order.order_no} - {self.product_name}"

    def save(self, *args, **kwargs):
        # 自动计算小计
        self.total_price = self.price * self.quantity
        
        # 保存商品快照信息
        if not self.product_name:
            self.product_name = self.product.name
        if not self.product_sku:
            self.product_sku = self.product.sku
        if not self.product_image:
            self.product_image = self.product.main_image.url if self.product.main_image else ''
        
        super().save(*args, **kwargs)

    @property
    def can_review(self):
        """是否可以评价"""
        return self.order.can_review


class OrderStatusLog(models.Model):
    """订单状态变更日志"""
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='status_logs', verbose_name='订单')
    old_status = models.CharField(max_length=20, verbose_name='原状态')
    new_status = models.CharField(max_length=20, verbose_name='新状态')
    operator = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='操作人')
    note = models.TextField(null=True, blank=True, verbose_name='备注')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '订单状态日志'
        verbose_name_plural = '订单状态日志'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.order.order_no} - {self.old_status} -> {self.new_status}"


class OrderManager:
    """订单管理器"""
    
    @staticmethod
    def create_order_from_cart(user, address_data, note=''):
        """从购物车创建订单"""
        from cart.models import Cart
        
        try:
            cart = Cart.objects.get(user=user)
            selected_items = cart.selected_items.select_related('product')
            
            if not selected_items.exists():
                return None, "请选择要结算的商品"
            
            # 检查库存
            for item in selected_items:
                if not item.is_available:
                    return None, f"商品 {item.product.name} 库存不足或已下架"
            
            # 计算金额
            total_amount = sum(item.total_price for item in selected_items)
            
            # 获取最优优惠
            discount_rule = DiscountRule.get_best_discount(total_amount)
            discount_amount = discount_rule.calculate_discount(total_amount) if discount_rule else 0
            
            # 计算运费（这里简化为免费）
            shipping_fee = 0
            final_amount = total_amount - discount_amount + shipping_fee
            
            # 创建订单
            order = Order.objects.create(
                user=user,
                total_amount=total_amount,
                discount_amount=discount_amount,
                shipping_fee=shipping_fee,
                final_amount=final_amount,
                discount_rule=discount_rule,
                note=note,
                **address_data
            )
            
            # 创建订单商品项
            for item in selected_items:
                OrderItem.objects.create(
                    order=order,
                    product=item.product,
                    quantity=item.quantity,
                    price=item.product.price,
                )
                
                # 减少库存，增加销量
                item.product.reduce_stock(item.quantity)
            
            # 清空购物车中的已选商品
            cart.clear_selected()
            
            return order, "订单创建成功"
            
        except Cart.DoesNotExist:
            return None, "购物车为空"

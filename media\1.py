<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>智能电商 - 时尚购物平台</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  
  <!-- 配置Tailwind主题 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#FF7D00',       // 主色调-橙色
            secondary: '#FF9E3B',     // 浅橙色
            accent: '#FF5A00',        // 深橙色
            dark: '#333333',          // 深色文本
            light: '#F9F5F0',         // 浅色背景
          },
          fontFamily: {
            sans: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .text-shadow {
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      .card-hover {
        @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
      }
      .btn-primary {
        @apply bg-primary text-white font-medium py-2 px-6 rounded-lg shadow-md hover:bg-accent hover:shadow-lg transition-all duration-300;
      }
      .btn-secondary {
        @apply bg-white text-primary border border-primary font-medium py-2 px-6 rounded-lg shadow-sm hover:bg-light transition-all duration-300;
      }
      .nav-link {
        @apply relative text-dark hover:text-primary transition-colors duration-300;
      }
      .nav-link::after {
        content: '';
        position: absolute;
        width: 0;
        height: 2px;
        bottom: -4px;
        left: 0;
        background-color: theme('colors.primary');
        transition: width 0.3s ease;
      }
      .nav-link:hover::after {
        width: 100%;
      }
      .carousel {
        position: relative;
        overflow: hidden;
      }
      .carousel-slides {
        display: flex;
        transition: transform 0.5s ease-in-out;
      }
      .carousel-slide {
        min-width: 100%;
      }
      .carousel-dot.active {
        background-color: white;
      }
    }
  </style>
</head>

<body class="bg-light text-dark font-sans">
  <!-- 顶部通知栏 -->
  <div class="bg-primary text-white text-center py-2 text-sm">
    <p>新用户注册立享<span class="font-bold">满100减20</span>优惠！| 会员积分可兑换无门槛优惠券</p>
  </div>

  <!-- 导航栏 -->
  <header class="sticky top-0 z-50 bg-white shadow-md transition-all duration-300">
    <div class="container mx-auto px-4">
      <div class="flex flex-col md:flex-row items-center justify-between py-4">
        <!-- Logo -->
        <a href="#" class="flex items-center mb-4 md:mb-0">
          <i class="fa fa-shopping-bag text-3xl text-primary mr-2"></i>
          <span class="text-2xl font-bold text-dark">智能电商</span>
        </a>
        
        <!-- 搜索框 -->
        <div class="w-full md:w-2/3 mb-4 md:mb-0 relative">
          <input 
            type="text" 
            placeholder="搜索商品, 如'iPhone 15 Pro Max'" 
            class="w-full pl-10 pr-4 py-3 rounded-full border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all"
          >
          <i class="fa fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-primary">
            <i class="fa fa-microphone"></i>
          </div>
        </div>
        
        <!-- 用户操作区 -->
        <div class="flex items-center space-x-4">
          <a href="#" class="nav-link flex items-center">
            <i class="fa fa-user-o mr-1"></i>
            <span>登录</span>
          </a>
          <a href="#" class="nav-link flex items-center">
            <i class="fa fa-user-plus mr-1"></i>
            <span>注册</span>
          </a>
          <a href="#" class="nav-link relative flex items-center">
            <i class="fa fa-shopping-cart mr-1"></i>
            <span>购物车</span>
            <span class="absolute -top-2 -right-5 bg-accent text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
          </a>
          <a href="#" class="nav-link flex items-center">
            <i class="fa fa-heart-o mr-1"></i>
            <span>收藏</span>
          </a>
        </div>
      </div>
      
      <!-- 分类导航 -->
      <div class="hidden md:flex items-center space-x-1 overflow-x-auto pb-2">
        <a href="#" class="nav-link whitespace-nowrap px-3 py-2">首页</a>
        <a href="#" class="nav-link whitespace-nowrap px-3 py-2">手机数码</a>
        <a href="#" class="nav-link whitespace-nowrap px-3 py-2">电脑办公</a>
        <a href="#" class="nav-link whitespace-nowrap px-3 py-2">家用电器</a>
        <a href="#" class="nav-link whitespace-nowrap px-3 py-2">服装服饰</a>
        <a href="#" class="nav-link whitespace-nowrap px-3 py-2">鞋包配饰</a>
        <a href="#" class="nav-link whitespace-nowrap px-3 py-2">美妆个护</a>
        <a href="#" class="nav-link whitespace-nowrap px-3 py-2">母婴用品</a>
        <a href="#" class="nav-link whitespace-nowrap px-3 py-2">食品生鲜</a>
        <a href="#" class="nav-link whitespace-nowrap px-3 py-2">运动户外</a>
        <a href="#" class="nav-link whitespace-nowrap px-3 py-2">家居生活</a>
        <a href="#" class="nav-link whitespace-nowrap px-3 py-2">汽车用品</a>
      </div>
    </div>
  </header>

  <main class="container mx-auto px-4 py-6">
    <!-- 轮播图 -->
    <div class="relative rounded-2xl overflow-hidden shadow-lg mb-8">
      <div class="carousel relative">
        <div class="carousel-slides flex transition-transform duration-500">
          <!-- 轮播图1 -->
          <div class="carousel-slide min-w-full">
            <img 
              src="https://picsum.photos/1600/500?random=1" 
              alt="夏季新品促销" 
              class="w-full h-[400px] md:h-[500px] object-cover"
            >
            <div class="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex items-center">
              <div class="w-1/2 md:w-1/3 px-8 text-white">
                <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold text-shadow mb-4">夏季新品上市</h2>
                <p class="text-lg mb-6 text-shadow">精选时尚单品，满300减50，满1000减200</p>
                <a href="#" class="btn-primary">立即抢购</a>
              </div>
            </div>
          </div>
          
          <!-- 轮播图2 -->
          <div class="carousel-slide min-w-full">
            <img 
              src="https://picsum.photos/1600/500?random=2" 
              alt="数码产品特惠" 
              class="w-full h-[400px] md:h-[500px] object-cover"
            >
            <div class="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex items-center">
              <div class="w-1/2 md:w-1/3 px-8 text-white">
                <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold text-shadow mb-4">数码产品特惠</h2>
                <p class="text-lg mb-6 text-shadow">手机电脑全场9折，下单送好礼</p>
                <a href="#" class="btn-primary">查看详情</a>
              </div>
            </div>
          </div>
          
          <!-- 轮播图3 -->
          <div class="carousel-slide min-w-full">
            <img 
              src="https://picsum.photos/1600/500?random=3" 
              alt="会员专享福利" 
              class="w-full h-[400px] md:h-[500px] object-cover"
            >
            <div class="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex items-center">
              <div class="w-1/2 md:w-1/3 px-8 text-white">
                <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold text-shadow mb-4">会员专享福利</h2>
                <p class="text-lg mb-6 text-shadow">升级VIP会员，享专属折扣和优先发货</p>
                <a href="#" class="btn-primary">立即升级</a>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 轮播图指示器 -->
        <div class="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
          <button class="carousel-dot w-3 h-3 rounded-full bg-white/50 focus:bg-white transition-colors active"></button>
          <button class="carousel-dot w-3 h-3 rounded-full bg-white/50 focus:bg-white transition-colors"></button>
          <button class="carousel-dot w-3 h-3 rounded-full bg-white/50 focus:bg-white transition-colors"></button>
        </div>
        
        <!-- 轮播图左右按钮 -->
        <button class="carousel-prev absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 rounded-full bg-white/30 backdrop-blur-sm text-white flex items-center justify-center hover:bg-white/50 transition-colors">
          <i class="fa fa-angle-left text-2xl"></i>
        </button>
        <button class="carousel-next absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 rounded-full bg-white/30 backdrop-blur-sm text-white flex items-center justify-center hover:bg-white/50 transition-colors">
          <i class="fa fa-angle-right text-2xl"></i>
        </button>
      </div>
    </div>

    <!-- 快捷分类导航 -->
    <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
      <a href="#" class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm card-hover">
        <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-2">
          <i class="fa fa-mobile text-2xl text-primary"></i>
        </div>
        <span class="text-sm">手机数码</span>
      </a>
      <a href="#" class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm card-hover">
        <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-2">
          <i class="fa fa-laptop text-2xl text-primary"></i>
        </div>
        <span class="text-sm">电脑办公</span>
      </a>
      <a href="#" class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm card-hover">
        <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-2">
          <i class="fa fa-home text-2xl text-primary"></i>
        </div>
        <span class="text-sm">家用电器</span>
      </a>
      <a href="#" class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm card-hover">
        <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-2">
          <i class="fa fa-tshirt text-2xl text-primary"></i>
        </div>
        <span class="text-sm">服装服饰</span>
      </a>
      <a href="#" class="flex flex-col items-center p-4 bg-white rounded-xl shadow-sm card-hover">
        <div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-2">
          <i class="fa fa-shopping-bag text-2xl text-primary"></i>
        </div>
        <span class="text-sm">更多分类</span>
      </a>
    </div>

    <!-- 促销活动区 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div class="bg-gradient-to-r from-primary to-accent rounded-2xl overflow-hidden shadow-lg card-hover">
        <div class="p-6 text-white">
          <h3 class="text-xl font-bold mb-2">新人专享</h3>
          <p class="mb-4">注册即送188元新人礼包</p>
          <a href="#" class="inline-block btn-secondary">立即注册</a>
        </div>
      </div>
      <div class="bg-gradient-to-r from-secondary to-primary rounded-2xl overflow-hidden shadow-lg card-hover">
        <div class="p-6 text-white">
          <h3 class="text-xl font-bold mb-2">限时秒杀</h3>
          <p class="mb-4">每日10点/20点限时秒杀</p>
          <a href="#" class="inline-block btn-secondary">查看活动</a>
        </div>
      </div>
      <div class="bg-gradient-to-r from-accent to-secondary rounded-2xl overflow-hidden shadow-lg card-hover">
        <div class="p-6 text-white">
          <h3 class="text-xl font-bold mb-2">会员福利</h3>
          <p class="mb-4">VIP会员享85折优惠</p>
          <a href="#" class="inline-block btn-secondary">立即升级</a>
        </div>
      </div>
    </div>

    <!-- 热门商品区 -->
    <section class="mb-12">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold">热门商品</h2>
        <a href="#" class="text-primary flex items-center">
          查看全部 <i class="fa fa-angle-right ml-1"></i>
        </a>
      </div>
      
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <!-- 商品卡片1 -->
        <div class="bg-white rounded-xl overflow-hidden shadow-sm card-hover">
          <div class="relative">
            <img src="https://picsum.photos/300/300?random=101" alt="潮流工装风多口袋休闲裤" class="w-full h-48 object-cover">
            <span class="absolute top-2 left-2 bg-accent text-white text-xs px-2 py-1 rounded">热销</span>
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
              <i class="fa fa-heart-o"></i>
            </button>
          </div>
          <div class="p-3">
            <h3 class="text-sm font-medium line-clamp-2 mb-1">潮流工装风多口袋休闲裤</h3>
            <div class="flex items-center justify-between">
              <span class="text-primary font-bold">¥199</span>
              <button class="bg-primary/10 text-primary text-xs px-2 py-1 rounded">加入购物车</button>
            </div>
          </div>
        </div>
        
        <!-- 商品卡片2 -->
        <div class="bg-white rounded-xl overflow-hidden shadow-sm card-hover">
          <div class="relative">
            <img src="https://picsum.photos/300/300?random=102" alt="法式复古方领碎花连衣裙" class="w-full h-48 object-cover">
            <span class="absolute top-2 left-2 bg-secondary text-white text-xs px-2 py-1 rounded">新品</span>
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
              <i class="fa fa-heart-o"></i>
            </button>
          </div>
          <div class="p-3">
            <h3 class="text-sm font-medium line-clamp-2 mb-1">法式复古方领碎花连衣裙</h3>
            <div class="flex items-center justify-between">
              <span class="text-primary font-bold">¥299</span>
              <button class="bg-primary/10 text-primary text-xs px-2 py-1 rounded">加入购物车</button>
            </div>
          </div>
        </div>
        
        <!-- 商品卡片3 -->
        <div class="bg-white rounded-xl overflow-hidden shadow-sm card-hover">
          <div class="relative">
            <img src="https://picsum.photos/300/300?random=103" alt="卡通恐龙图案连帽卫衣" class="w-full h-48 object-cover">
            <span class="absolute top-2 left-2 bg-primary text-white text-xs px-2 py-1 rounded">热卖</span>
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
              <i class="fa fa-heart-o"></i>
            </button>
          </div>
          <div class="p-3">
            <h3 class="text-sm font-medium line-clamp-2 mb-1">卡通恐龙图案连帽卫衣</h3>
            <div class="flex items-center justify-between">
              <span class="text-primary font-bold">¥159</span>
              <button class="bg-primary/10 text-primary text-xs px-2 py-1 rounded">加入购物车</button>
            </div>
          </div>
        </div>
        
        <!-- 商品卡片4 -->
        <div class="bg-white rounded-xl overflow-hidden shadow-sm card-hover">
          <div class="relative">
            <img src="https://picsum.photos/300/300?random=104" alt="玻尿酸保湿补水面膜" class="w-full h-48 object-cover">
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
              <i class="fa fa-heart-o"></i>
            </button>
          </div>
          <div class="p-3">
            <h3 class="text-sm font-medium line-clamp-2 mb-1">玻尿酸保湿补水面膜</h3>
            <div class="flex items-center justify-between">
              <span class="text-primary font-bold">¥89</span>
              <button class="bg-primary/10 text-primary text-xs px-2 py-1 rounded">加入购物车</button>
            </div>
          </div>
        </div>
        
        <!-- 商品卡片5 -->
        <div class="bg-white rounded-xl overflow-hidden shadow-sm card-hover">
          <div class="relative">
            <img src="https://picsum.photos/300/300?random=105" alt="雾面哑光口红" class="w-full h-48 object-cover">
            <span class="absolute top-2 left-2 bg-secondary text-white text-xs px-2 py-1 rounded">新品</span>
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
              <i class="fa fa-heart-o"></i>
            </button>
          </div>
          <div class="p-3">
            <h3 class="text-sm font-medium line-clamp-2 mb-1">雾面哑光口红</h3>
            <div class="flex items-center justify-between">
              <span class="text-primary font-bold">¥129</span>
              <button class="bg-primary/10 text-primary text-xs px-2 py-1 rounded">加入购物车</button>
            </div>
          </div>
        </div>
        
        <!-- 商品卡片6 -->
        <div class="bg-white rounded-xl overflow-hidden shadow-sm card-hover">
          <div class="relative">
            <img src="https://picsum.photos/300/300?random=106" alt="磨砂去角质沐浴露" class="w-full h-48 object-cover">
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
              <i class="fa fa-heart-o"></i>
            </button>
          </div>
          <div class="p-3">
            <h3 class="text-sm font-medium line-clamp-2 mb-1">磨砂去角质沐浴露</h3>
            <div class="flex items-center justify-between">
              <span class="text-primary font-bold">¥69</span>
              <button class="bg-primary/10 text-primary text-xs px-2 py-1 rounded">加入购物车</button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 新品上市区 -->
    <section class="mb-12">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold">新品上市</h2>
        <a href="#" class="text-primary flex items-center">
          查看全部 <i class="fa fa-angle-right ml-1"></i>
        </a>
      </div>
      
      <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
        <!-- 新品卡片1 -->
        <div class="bg-white rounded-xl overflow-hidden shadow-sm card-hover">
          <div class="relative">
            <img src="https://picsum.photos/300/300?random=111" alt="全棉磨毛四件套" class="w-full h-40 object-cover">
            <span class="absolute top-2 left-2 bg-accent text-white text-xs px-2 py-1 rounded">新品</span>
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
              <i class="fa fa-heart-o"></i>
            </button>
          </div>
          <div class="p-2">
            <h3 class="text-xs font-medium line-clamp-2 mb-1">全棉磨毛四件套</h3>
            <span class="text-primary font-bold text-xs">¥399</span>
          </div>
        </div>
        
        <!-- 新品卡片2-8（类似结构，不同图片和名称） -->
        <div class="bg-white rounded-xl overflow-hidden shadow-sm card-hover">
          <div class="relative">
            <img src="https://picsum.photos/300/300?random=112" alt="不粘涂层平底锅" class="w-full h-40 object-cover">
            <span class="absolute top-2 left-2 bg-accent text-white text-xs px-2 py-1 rounded">新品</span>
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
              <i class="fa fa-heart-o"></i>
            </button>
          </div>
          <div class="p-2">
            <h3 class="text-xs font-medium line-clamp-2 mb-1">不粘涂层平底锅</h3>
            <span class="text-primary font-bold text-xs">¥169</span>
          </div>
        </div>
        
        <div class="bg-white rounded-xl overflow-hidden shadow-sm card-hover">
          <div class="relative">
            <img src="https://picsum.photos/300/300?random=113" alt="抽屉式塑料收纳箱" class="w-full h-40 object-cover">
            <span class="absolute top-2 left-2 bg-accent text-white text-xs px-2 py-1 rounded">新品</span>
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
              <i class="fa fa-heart-o"></i>
            </button>
          </div>
          <div class="p-2">
            <h3 class="text-xs font-medium line-clamp-2 mb-1">抽屉式塑料收纳箱</h3>
            <span class="text-primary font-bold text-xs">¥89</span>
          </div>
        </div>
        
        <div class="bg-white rounded-xl overflow-hidden shadow-sm card-hover">
          <div class="relative">
            <img src="https://picsum.photos/300/300?random=114" alt="磁吸无线充电宝" class="w-full h-40 object-cover">
            <span class="absolute top-2 left-2 bg-accent text-white text-xs px-2 py-1 rounded">新品</span>
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
              <i class="fa fa-heart-o"></i>
            </button>
          </div>
          <div class="p-2">
            <h3 class="text-xs font-medium line-clamp-2 mb-1">磁吸无线充电宝</h3>
            <span class="text-primary font-bold text-xs">¥199</span>
          </div>
        </div>
        
        <div class="bg-white rounded-xl overflow-hidden shadow-sm card-hover">
          <div class="relative">
            <img src="https://picsum.photos/300/300?random=115" alt="便携式蓝牙键盘" class="w-full h-40 object-cover">
            <span class="absolute top-2 left-2 bg-accent text-white text-xs px-2 py-1 rounded">新品</span>
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
              <i class="fa fa-heart-o"></i>
            </button>
          </div>
          <div class="p-2">
            <h3 class="text-xs font-medium line-clamp-2 mb-1">便携式蓝牙键盘</h3>
            <span class="text-primary font-bold text-xs">¥249</span>
          </div>
        </div>
        
        <div class="bg-white rounded-xl overflow-hidden shadow-sm card-hover">
          <div class="relative">
            <img src="https://picsum.photos/300/300?random=116" alt="微单相机" class="w-full h-40 object-cover">
            <span class="absolute top-2 left-2 bg-accent text-white text-xs px-2 py-1 rounded">新品</span>
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
              <i class="fa fa-heart-o"></i>
            </button>
          </div>
          <div class="p-2">
            <h3 class="text-xs font-medium line-clamp-2 mb-1">微单相机</h3>
            <span class="text-primary font-bold text-xs">¥4999</span>
          </div>
        </div>
        
        <div class="bg-white rounded-xl overflow-hidden shadow-sm card-hover">
          <div class="relative">
            <img src="https://picsum.photos/300/300?random=117" alt="双开门风冷无霜冰箱" class="w-full h-40 object-cover">
            <span class="absolute top-2 left-2 bg-accent text-white text-xs px-2 py-1 rounded">新品</span>
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
              <i class="fa fa-heart-o"></i>
            </button>
          </div>
          <div class="p-2">
            <h3 class="text-xs font-medium line-clamp-2 mb-1">双开门风冷无霜冰箱</h3>
            <span class="text-primary font-bold text-xs">¥5999</span>
          </div>
        </div>
        
        <div class="bg-white rounded-xl overflow-hidden shadow-sm card-hover">
          <div class="relative">
            <img src="https://picsum.photos/300/300?random=118" alt="多功能空气炸锅" class="w-full h-40 object-cover">
            <span class="absolute top-2 left-2 bg-accent text-white text-xs px-2 py-1 rounded">新品</span>
            <button class="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors">
              <i class="fa fa-heart-o"></i>
            </button>
          </div>
          <div class="p-2">
            <h3 class="text-xs font-medium line-clamp-2 mb-1">多功能空气炸锅</h3>
            <span class="text-primary font-bold text-xs">¥399</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 品牌专区 -->
    <section class="mb-12">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold">精选品牌</h2>
        <a href="#" class="text-primary flex items-center">
          更多品牌 <i class="fa fa-angle-right ml-1"></i>
        </a>
      </div>
      
      <div class="grid grid-cols-3 md:grid-cols-6 lg:grid-cols-8 gap-4">
        <a href="#" class="bg-white rounded-lg p-4 flex items-center justify-center card-hover">
          <img src="https://picsum.photos/100/50?random=201" alt="品牌1" class="h-12 opacity-70 hover:opacity-100 transition-opacity">
        </a>
        <a href="#" class="bg-white rounded-lg p-4 flex items-center justify-center card-hover">
          <img src="https://picsum.photos/100/50?random=202" alt="品牌2" class="h-12 opacity-70 hover:opacity-100 transition-opacity">
        </a>
        <a href="#" class="bg-white rounded-lg p-4 flex items-center justify-center card-hover">
          <img src="https://picsum.photos/100/50?random=203" alt="品牌3" class="h-12 opacity-70 hover:opacity-100 transition-opacity">
        </a>
        <a href="#" class="bg-white rounded-lg p-4 flex items-center justify-center card-hover">
          <img src="https://picsum.photos/100/50?random=204" alt="品牌4" class="h-12 opacity-70 hover:opacity-100 transition-opacity">
        </a>
        <a href="#" class="bg-white rounded-lg p-4 flex items-center justify-center card-hover">
          <img src="https://picsum.photos/100/50?random=205" alt="品牌5" class="h-12 opacity-70 hover:opacity-100 transition-opacity">
        </a>
        <a href="#" class="bg-white rounded-lg p-4 flex items-center justify-center card-hover">
          <img src="https://picsum.photos/100/50?random=206" alt="品牌6" class="h-12 opacity-70 hover:opacity-100 transition-opacity">
        </a>
        <a href="#" class="bg-white rounded-lg p-4 flex items-center justify-center card-hover">
          <img src="https://picsum.photos/100/50?random=207" alt="品牌7" class="h-12 opacity-70 hover:opacity-100 transition-opacity">
        </a>
        <a href="#" class="bg-white rounded-lg p-4 flex items-center justify-center card-hover">
          <img src="https://picsum.photos/100/50?random=208" alt="品牌8"
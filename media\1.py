<!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>购物商城首页</title>
            <script src="https://cdn.tailwindcss.com"></script>
            <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
            <link rel="stylesheet" href="styles.css">
        </head>
        <body class="font-sans bg-gray-50">
            <!-- 顶部导航 -->
            <header class="sticky top-0 z-50 bg-white shadow-sm">
                <div class="container mx-auto px-4">
                    <div class="flex items-center justify-between py-3">
                        <div class="flex items-center">
                            <a href="#" class="text-2xl font-bold text-blue-600">购物商城</a>
                        </div>
                        
                        <div class="hidden md:flex items-center space-x-4">
                            <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors">首页</a>
                            <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors">分类</a>
                            <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors">促销</a>
                            <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors">新品</a>
                            <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors">联系我们</a>
                        </div>
                        
                        <div class="flex items-center space-x-4">
                            <div class="relative hidden md:block">
                                <input type="text" placeholder="搜索商品..." class="w-64 py-2 px-4 rounded-full border focus:outline-none focus:ring-2 focus:ring-blue-300">
                                <button class="absolute right-3 top-2 text-gray-400 hover:text-blue-600">
                                    <i class="fa fa-search"></i>
                                </button>
                            </div>
                            
                            <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors">
                                <i class="fa fa-user-circle-o text-xl"></i>
                            </a>
                            
                            <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors relative">
                                <i class="fa fa-shopping-cart text-xl"></i>
                                <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                            </a>
                            
                            <button class="md:hidden text-gray-700 hover:text-blue-600 transition-colors" id="mobile-menu-button">
                                <i class="fa fa-bars text-xl"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 移动端菜单 -->
                    <div class="md:hidden hidden" id="mobile-menu">
                        <div class="py-2 space-y-1">
                            <a href="#" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">首页</a>
                            <a href="#" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">分类</a>
                            <a href="#" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">促销</a>
                            <a href="#" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">新品</a>
                            <a href="#" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md">联系我们</a>
                            <div class="relative mt-3">
                                <input type="text" placeholder="搜索商品..." class="w-full py-2 px-4 rounded-full border focus:outline-none focus:ring-2 focus:ring-blue-300">
                                <button class="absolute right-3 top-2 text-gray-400 hover:text-blue-600">
                                    <i class="fa fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 登录注册模态框 -->
            <div id="auth-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
                <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 transform transition-all">
                    <div class="flex justify-between items-center p-4 border-b">
                        <h3 class="text-xl font-semibold text-gray-800">用户登录</h3>
                        <button id="close-modal" class="text-gray-500 hover:text-gray-700">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                    <div class="p-6">
                        <div class="flex space-x-2 mb-6">
                            <button id="login-tab" class="flex-1 py-2 px-4 text-center font-medium text-blue-600 border-b-2 border-blue-600">登录</button>
                            <button id="register-tab" class="flex-1 py-2 px-4 text-center font-medium text-gray-500 border-b-2 border-transparent">注册</button>
                        </div>
                        
                        <!-- 登录表单 -->
                        <div id="login-form">
                            <div class="mb-4">
                                <label for="login-email" class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                                <input type="email" id="login-email" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-300">
                            </div>
                            <div class="mb-6">
                                <label for="login-password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                                <input type="password" id="login-password" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-300">
                            </div>
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <input type="checkbox" id="remember-me" class="h-4 w-4 text-blue-600 focus:ring-blue-300 border-gray-300 rounded">
                                    <label for="remember-me" class="ml-2 block text-sm text-gray-700">记住我</label>
                                </div>
                                <a href="#" class="text-sm text-blue-600 hover:text-blue-500">忘记密码?</a>
                            </div>
                            <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors">登录</button>
                        </div>
                        
                        <!-- 注册表单 -->
                        <div id="register-form" class="hidden">
                            <div class="mb-4">
                                <label for="register-name" class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                                <input type="text" id="register-name" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-300">
                            </div>
                            <div class="mb-4">
                                <label for="register-email" class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                                <input type="email" id="register-email" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-300">
                            </div>
                            <div class="mb-4">
                                <label for="register-password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                                <input type="password" id="register-password" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-300">
                            </div>
                            <div class="mb-6">
                                <label for="register-confirm-password" class="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
                                <input type="password" id="register-confirm-password" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-300">
                            </div>
                            <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors">注册</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 轮播图 -->
            <section class="relative overflow-hidden">
                <div id="carousel" class="flex transition-transform duration-500 ease-in-out">
                    <div class="w-full flex-shrink-0 relative">
                        <img src="https://picsum.photos/1600/500?random=1" alt="夏季大促销" class="w-full h-[500px] object-cover">
                        <div class="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex items-center">
                            <div class="container mx-auto px-4 text-white max-w-xl">
                                <h2 class="text-[clamp(2rem,5vw,3.5rem)] font-bold leading-tight mb-4">夏季大促销</h2>
                                <p class="text-[clamp(1rem,2vw,1.25rem)] mb-6">限时折扣，全场低至3折，快来选购吧！</p>
                                <button class="bg-white text-blue-600 hover:bg-blue-50 py-3 px-8 rounded-full font-medium transition-colors">立即购买</button>
                            </div>
                        </div>
                    </div>
                    <div class="w-full flex-shrink-0 relative">
                        <img src="https://picsum.photos/1600/500?random=2" alt="新品上市" class="w-full h-[500px] object-cover">
                        <div class="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex items-center">
                            <div class="container mx-auto px-4 text-white max-w-xl">
                                <h2 class="text-[clamp(2rem,5vw,3.5rem)] font-bold leading-tight mb-4">新品上市</h2>
                                <p class="text-[clamp(1rem,2vw,1.25rem)] mb-6">2025最新款商品，时尚潮流，品质保证！</p>
                                <button class="bg-white text-blue-600 hover:bg-blue-50 py-3 px-8 rounded-full font-medium transition-colors">查看详情</button>
                            </div>
                        </div>
                    </div>
                    <div class="w-full flex-shrink-0 relative">
                        <img src="https://picsum.photos/1600/500?random=3" alt="会员专享" class="w-full h-[500px] object-cover">
                        <div class="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex items-center">
                            <div class="container mx-auto px-4 text-white max-w-xl">
                                <h2 class="text-[clamp(2rem,5vw,3.5rem)] font-bold leading-tight mb-4">会员专享</h2>
                                <p class="text-[clamp(1rem,2vw,1.25rem)] mb-6">成为会员，享受专属优惠和贴心服务！</p>
                                <button class="bg-white text-blue-600 hover:bg-blue-50 py-3 px-8 rounded-full font-medium transition-colors">立即加入</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 轮播图导航按钮 -->
                <button id="prev-btn" class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/70 hover:bg-white text-blue-600 w-10 h-10 rounded-full flex items-center justify-center transition-colors">
                    <i class="fa fa-angle-left text-xl"></i>
                </button>
                <button id="next-btn" class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/70 hover:bg-white text-blue-600 w-10 h-10 rounded-full flex items-center justify-center transition-colors">
                    <i class="fa fa-angle-right text-xl"></i>
                </button>
                
                <!-- 轮播图指示器 -->
                <div class="absolute bottom-6 left-0 right-0 flex justify-center space-x-2">
                    <button class="w-3 h-3 rounded-full bg-white/50 hover:bg-white focus:outline-none carousel-indicator active" data-index="0"></button>
                    <button class="w-3 h-3 rounded-full bg-white/50 hover:bg-white focus:outline-none carousel-indicator" data-index="1"></button>
                    <button class="w-3 h-3 rounded-full bg-white/50 hover:bg-white focus:outline-none carousel-indicator" data-index="2"></button>
                </div>
            </section>
            
            <!-- 分类导航 -->
            <section class="py-12 bg-white">
                <div class="container mx-auto px-4">
                    <h2 class="text-2xl font-bold text-gray-800 mb-8 text-center">商品分类</h2>
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        <a href="#" class="group">
                            <div class="bg-gray-50 rounded-xl p-6 text-center hover:shadow-md transition-shadow">
                                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                                    <i class="fa fa-laptop text-blue-600 text-2xl"></i>
                                </div>
                                <h3 class="font-medium text-gray-800">电子产品</h3>
                            </div>
                        </a>
                        <a href="#" class="group">
                            <div class="bg-gray-50 rounded-xl p-6 text-center hover:shadow-md transition-shadow">
                                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                                    <i class="fa fa-tshirt text-purple-600 text-2xl"></i>
                                </div>
                                <h3 class="font-medium text-gray-800">服装鞋帽</h3>
                            </div>
                        </a>
                        <a href="#" class="group">
                            <div class="bg-gray-50 rounded-xl p-6 text-center hover:shadow-md transition-shadow">
                                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors">
                                    <i class="fa fa-cutlery text-green-600 text-2xl"></i>
                                </div>
                                <h3 class="font-medium text-gray-800">食品饮料</h3>
                            </div>
                        </a>
                        <a href="#" class="group">
                            <div class="bg-gray-50 rounded-xl p-6 text-center hover:shadow-md transition-shadow">
                                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-red-200 transition-colors">
                                    <i class="fa fa-gift text-red-600 text-2xl"></i>
                                </div>
                                <h3 class="font-medium text-gray-800">礼品礼物</h3>
                            </div>
                        </a>
                        <a href="#" class="group">
                            <div class="bg-gray-50 rounded-xl p-6 text-center hover:shadow-md transition-shadow">
                                <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-yellow-200 transition-colors">
                                    <i class="fa fa-book text-yellow-600 text-2xl"></i>
                                </div>
                                <h3 class="font-medium text-gray-800">图书音像</h3>
                            </div>
                        </a>
                        <a href="#" class="group">
                            <div class="bg-gray-50 rounded-xl p-6 text-center hover:shadow-md transition-shadow">
                                <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-indigo-200 transition-colors">
                                    <i class="fa fa-ellipsis-h text-indigo-600 text-2xl"></i>
                                </div>
                                <h3 class="font-medium text-gray-800">更多分类</h3>
                            </div>
                        </a>
                    </div>
                </div>
            </section>
            
            <!-- 热门推荐 -->
            <section class="py-12 bg-gray-50">
                <div class="container mx-auto px-4">
                    <div class="flex justify-between items-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-800">热门推荐</h2>
                        <a href="#" class="text-blue-600 hover:text-blue-700 flex items-center">
                            查看全部 <i class="fa fa-angle-right ml-1"></i>
                        </a>
                    </div>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- 商品卡片1 -->
                        <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                            <div class="relative">
                                <img src="https://picsum.photos/400/400?random=10" alt="智能手表" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                                <div class="absolute top-3 left-3">
                                    <span class="bg-red-500 text-white text-xs font-medium py-1 px-2 rounded">热销</span>
                                </div>
                                <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                                    <i class="fa fa-heart-o"></i>
                                </button>
                            </div>
                            <div class="p-4">
                                <div class="flex items-center mb-2">
                                    <div class="flex text-yellow-400">
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star-half-o"></i>
                                    </div>
                                    <span class="text-gray-500 text-sm ml-2">4.5 (128)</span>
                                </div>
                                <h3 class="font-medium text-gray-800 mb-1">智能手表 Pro</h3>
                                <p class="text-gray-500 text-sm mb-3 line-clamp-2">健康监测、运动追踪、消息提醒，功能强大的智能手表。</p>
                                <div class="flex justify-between items-center">
                                    <div>
                                        <span class="text-red-600 font-bold">¥1,299</span>
                                        <span class="text-gray-400 line-through text-sm ml-2">¥1,599</span>
                                    </div>
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white w-10 h-10 rounded-full flex items-center justify-center transition-colors">
                                        <i class="fa fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 商品卡片2 -->
                        <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                            <div class="relative">
                                <img src="https://picsum.photos/400/400?random=11" alt="无线耳机" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                                <div class="absolute top-3 left-3">
                                    <span class="bg-green-500 text-white text-xs font-medium py-1 px-2 rounded">新品</span>
                                </div>
                                <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                                    <i class="fa fa-heart-o"></i>
                                </button>
                            </div>
                            <div class="p-4">
                                <div class="flex items-center mb-2">
                                    <div class="flex text-yellow-400">
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star-o"></i>
                                    </div>
                                    <span class="text-gray-500 text-sm ml-2">4.0 (86)</span>
                                </div>
                                <h3 class="font-medium text-gray-800 mb-1">无线降噪耳机</h3>
                                <p class="text-gray-500 text-sm mb-3 line-clamp-2">主动降噪，高清音质，舒适佩戴，长达30小时续航。</p>
                                <div class="flex justify-between items-center">
                                    <div>
                                        <span class="text-red-600 font-bold">¥899</span>
                                        <span class="text-gray-400 line-through text-sm ml-2">¥1,099</span>
                                    </div>
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white w-10 h-10 rounded-full flex items-center justify-center transition-colors">
                                        <i class="fa fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 商品卡片3 -->
                        <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                            <div class="relative">
                                <img src="https://picsum.photos/400/400?random=12" alt="运动背包" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                                <div class="absolute top-3 left-3">
                                    <span class="bg-orange-500 text-white text-xs font-medium py-1 px-2 rounded">促销</span>
                                </div>
                                <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                                    <i class="fa fa-heart-o"></i>
                                </button>
                            </div>
                            <div class="p-4">
                                <div class="flex items-center mb-2">
                                    <div class="flex text-yellow-400">
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                    </div>
                                    <span class="text-gray-500 text-sm ml-2">5.0 (42)</span>
                                </div>
                                <h3 class="font-medium text-gray-800 mb-1">专业运动背包</h3>
                                <p class="text-gray-500 text-sm mb-3 line-clamp-2">大容量，多隔层设计，防水耐磨，适合各种户外运动。</p>
                                <div class="flex justify-between items-center">
                                    <div>
                                        <span class="text-red-600 font-bold">¥299</span>
                                        <span class="text-gray-400 line-through text-sm ml-2">¥399</span>
                                    </div>
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white w-10 h-10 rounded-full flex items-center justify-center transition-colors">
                                        <i class="fa fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 商品卡片4 -->
                        <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group">
                            <div class="relative">
                                <img src="https://picsum.photos/400/400?random=13" alt="智能音箱" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                                <button class="absolute top-3 right-3 bg-white/80 hover:bg-white text-gray-700 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                                    <i class="fa fa-heart-o"></i>
                                </button>
                            </div>
                            <div class="p-4">
                                <div class="flex items-center mb-2">
                                    <div class="flex text-yellow-400">
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star-half-o"></i>
                                        <i class="fa fa-star-o"></i>
                                    </div>
                                    <span class="text-gray-500 text-sm ml-2">3.5 (67)</span>
                                </div>
                                <h3 class="font-medium text-gray-800 mb-1">智能语音助手音箱</h3>
                                <p class="text-gray-500 text-sm mb-3 line-clamp-2">支持多种语音指令，智能家居控制，高品质音乐播放。</p>
                                <div class="flex justify-between items-center">
                                    <div>
                                        <span class="text-red-600 font-bold">¥399</span>
                                        <span class="text-gray-400 line-through text-sm ml-2">¥499</span>
                                    </div>
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white w-10 h-10 rounded-full flex items-center justify-center transition-colors">
                                        <i class="fa fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- 限时秒杀 -->
            <section class="py-12 bg-white">
                <div class="container mx-auto px-4">
                    <div class="flex justify-between items-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-800">限时秒杀</h2>
                        <div class="flex items-center">
                            <span class="text-gray-600 mr-2">剩余时间:</span>
                            <div class="flex space-x-1">
                                <div class="bg-gray-800 text-white w-8 h-8 rounded flex items-center justify-center text-sm font-bold" id="hour">08</div>
                                <span class="text-gray-800">:</span>
                                <div class="bg-gray-800 text-white w-8 h-8 rounded flex items-center justify-center text-sm font-bold" id="minute">45</div>
                                <span class="text-gray-800">:</span>
                                <div class="bg-gray-800 text-white w-8 h-8 rounded flex items-center justify-center text-sm font-bold" id="second">30</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- 秒杀商品1 -->
                        <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group border border-gray-100">
                            <div class="relative">
                                <img src="https://picsum.photos/400/400?random=14" alt="蓝牙音箱" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                                <div class="absolute top-0 left-0 bg-red-500 text-white text-xs font-bold py-1 px-3">
                                    限时5折
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-medium text-gray-800 mb-2">便携式蓝牙音箱</h3>
                                <div class="flex items-center mb-3">
                                    <div class="flex-1">
                                        <div class="h-2 bg-gray-200 rounded-full overflow-hidden">
                                            <div class="h-full bg-red-500 rounded-full" style="width: 75%"></div>
                                        </div>
                                    </div>
                                    <span class="text-red-500 text-xs font-medium ml-2">75% 已售</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div>
                                        <span class="text-red-600 font-bold text-xl">¥199</span>
                                        <span class="text-gray-400 line-through text-sm ml-2">¥399</span>
                                    </div>
                                    <button class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-colors text-sm font-medium">
                                        立即抢购
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 秒杀商品2 -->
                        <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group border border-gray-100">
                            <div class="relative">
                                <img src="https://picsum.photos/400/400?random=15" alt="机械键盘" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                                <div class="absolute top-0 left-0 bg-red-500 text-white text-xs font-bold py-1 px-3">
                                    限时6折
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-medium text-gray-800 mb-2">机械键盘 Cherry 轴</h3>
                                <div class="flex items-center mb-3">
                                    <div class="flex-1">
                                        <div class="h-2 bg-gray-200 rounded-full overflow-hidden">
                                            <div class="h-full bg-red-500 rounded-full" style="width: 60%"></div>
                                        </div>
                                    </div>
                                    <span class="text-red-500 text-xs font-medium ml-2">60% 已售</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div>
                                        <span class="text-red-600 font-bold text-xl">¥359</span>
                                        <span class="text-gray-400 line-through text-sm ml-2">¥599</span>
                                    </div>
                                    <button class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-colors text-sm font-medium">
                                        立即抢购
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 秒杀商品3 -->
                        <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group border border-gray-100">
                            <div class="relative">
                                <img src="https://picsum.photos/400/400?random=16" alt="游戏鼠标" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                                <div class="absolute top-0 left-0 bg-red-500 text-white text-xs font-bold py-1 px-3">
                                    限时7折
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-medium text-gray-800 mb-2">专业游戏鼠标</h3>
                                <div class="flex items-center mb-3">
                                    <div class="flex-1">
                                        <div class="h-2 bg-gray-200 rounded-full overflow-hidden">
                                            <div class="h-full bg-red-500 rounded-full" style="width: 45%"></div>
                                        </div>
                                    </div>
                                    <span class="text-red-500 text-xs font-medium ml-2">45% 已售</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div>
                                        <span class="text-red-600 font-bold text-xl">¥139</span>
                                        <span class="text-gray-400 line-through text-sm ml-2">¥199</span>
                                    </div>
                                    <button class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-colors text-sm font-medium">
                                        立即抢购
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 秒杀商品4 -->
                        <div class="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow group border border-gray-100">
                            <div class="relative">
                                <img src="https://picsum.photos/400/400?random=17" alt="移动电源" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                                <div class="absolute top-0 left-0 bg-red-500 text-white text-xs font-bold py-1 px-3">
                                    限时4折
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-medium text-gray-800 mb-2">20000mAh 移动电源</h3>
                                <div class="flex items-center mb-3">
                                    <div class="flex-1">
                                        <div class="h-2 bg-gray-200 rounded-full overflow-hidden">
                                            <div class="h-full bg-red-500 rounded-full" style="width: 85%"></div>
                                        </div>
                                    </div>
                                    <span class="text-red-500 text-xs font-medium ml-2">85% 已售</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div>
                                        <span class="text-red-600 font-bold text-xl">¥119</span>
                                        <span class="text-gray-400 line-through text-sm ml-2">¥299</span>
                                    </div>
                                    <button class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-colors text-sm font-medium">
                                        立即抢购
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- 促销活动 -->
            <section class="py-12 bg-gray-50">
                <div class="container mx-auto px-4">
                    <h2 class="text-2xl font-bold text-gray-800 mb-8 text-center">促销活动</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <a href="#" class="block relative overflow-hidden rounded-xl group">
                            <img src="https://picsum.photos/800/400?random=20" alt="满减活动" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-500">
                            <div class="absolute inset-0 bg-gradient-to-r from-black/70 to-transparent flex items-center">
                                <div class="px-6 py-4 text-white max-w-xs">
                                    <h3 class="text-xl font-bold mb-2">满减特惠</h3>
                                    <p class="mb-4">全场满299减50，满599减120，满999减250！</p>
                                    <span class="inline-block bg-white text-blue-600 hover:bg-blue-50 py-2 px-4 rounded-md transition-colors text-sm font-medium">立即查看</span>
                                </div>
                            </div>
                        </a>
                        
                        <a href="#" class="block relative overflow-hidden rounded-xl group">
                            <img src="https://picsum.photos/800/400?random=21" alt="会员日" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-500">
                            <div class="absolute inset-0 bg-gradient-to-r from-black/70 to-transparent flex items-center">
                                <div class="px-6 py-4 text-white max-w-xs">
                                    <h3 class="text-xl font-bold mb-2">会员专享日</h3>
                                    <p class="mb-4">会员日额外9折，积分翻倍，限量好礼送不停！</p>
                                    <span class="inline-block bg-white text-blue-600 hover:bg-blue-50 py-2 px-4 rounded-md transition-colors text-sm font-medium">立即查看</span>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </section>
            
            <!-- 用户评价 -->
            <section class="py-12 bg-white">
                <div class="container mx-auto px-4">
                    <h2 class="text-2xl font-bold text-gray-800 mb-8 text-center">用户评价</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- 评价1 -->
                        <div class="bg-gray-50 p-6 rounded-xl">
                            <div class="flex items-center mb-4">
                                <img src="https://picsum.photos/100/100?random=30" alt="用户头像" class="w-12 h-12 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-medium text-gray-800">张三</h4>
                                    <div class="flex text-yellow-400 mt-1">
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-3">"这个智能手表的功能非常强大，续航也很长，使用了一个星期才充一次电，非常满意！"</p>
                            <div class="flex items-center text-gray-500 text-sm">
                                <span>2025年5月15日</span>
                                <span class="mx-2">|</span>
                                <span>智能手表 Pro</span>
                            </div>
                        </div>
                        
                        <!-- 评价2 -->
                        <div class="bg-gray-50 p-6 rounded-xl">
                            <div class="flex items-center mb-4">
                                <img src="https://picsum.photos/100/100?random=31" alt="用户头像" class="w-12 h-12 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-medium text-gray-800">李四</h4>
                                    <div class="flex text-yellow-400 mt-1">
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star-o"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-3">"无线耳机的音质非常好，降噪效果也不错，佩戴舒适，唯一不足的是充电盒有点大，携带不太方便。"</p>
                            <div class="flex items-center text-gray-500 text-sm">
                                <span>2025年5月20日</span>
                                <span class="mx-2">|</span>
                                <span>无线降噪耳机</span>
                            </div>
                        </div>
                        
                        <!-- 评价3 -->
                        <div class="bg-gray-50 p-6 rounded-xl">
                            <div class="flex items-center mb-4">
                                <img src="https://picsum.photos/100/100?random=32" alt="用户头像" class="w-12 h-12 rounded-full object-cover">
                                <div class="ml-4">
                                    <h4 class="font-medium text-gray-800">王五</h4>
                                    <div class="flex text-yellow-400 mt-1">
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star-half-o"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-3">"运动背包的质量非常好，容量大，设计合理，防水性能也不错，出去旅行用非常合适。物流也很快，值得推荐！"</p>
                            <div class="flex items-center text-gray-500 text-sm">
                                <span>2025年6月2日</span>
                                <span class="mx-2">|</span>
                                <span>专业运动背包</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- 订阅 -->        /* 基础样式 */
        body {
            font-family: 'Inter', sans-serif;
        }
        
        /* 轮播图指示器样式 */
        .carousel-indicator.active {
            background-color: white;
        }
        
        /* 商品卡片悬停效果 */
        .group:hover .group-hover\\:scale-105 {
            transform: scale(1.05);
        }
        
        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }
        
        /* 自定义动画 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .animate-fadeIn {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        /* 按钮过渡效果 */
        .transition-colors {
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        /* 骨架屏加载效果 */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
    
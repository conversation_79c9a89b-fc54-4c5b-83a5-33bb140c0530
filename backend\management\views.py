from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
import json

from .models import (
    Product, Category, Order, OrderItem, UserProfile, 
    Review, Banner, Cart, Favorite
)


def is_admin(user):
    """检查是否为管理员"""
    return user.is_authenticated and (user.is_staff or user.is_superuser)


def admin_login(request):
    """管理员登录"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        
        user = authenticate(request, username=username, password=password)
        if user and (user.is_staff or user.is_superuser):
            login(request, user)
            return redirect('management:dashboard')
        else:
            messages.error(request, '用户名或密码错误，或您没有管理员权限')
    
    return render(request, 'management/login.html')


def admin_logout(request):
    """管理员退出"""
    logout(request)
    return redirect('management:login')


def admin_register(request):
    """管理员注册"""
    if request.method == 'POST':
        username = request.POST.get('username')
        email = request.POST.get('email')
        password = request.POST.get('password')
        confirm_password = request.POST.get('confirm_password')
        
        # 验证
        if not all([username, email, password, confirm_password]):
            messages.error(request, '请填写完整信息')
            return render(request, 'management/register.html')
        
        if password != confirm_password:
            messages.error(request, '两次密码不一致')
            return render(request, 'management/register.html')
        
        if User.objects.filter(username=username).exists():
            messages.error(request, '用户名已存在')
            return render(request, 'management/register.html')
        
        if User.objects.filter(email=email).exists():
            messages.error(request, '邮箱已被注册')
            return render(request, 'management/register.html')
        
        # 创建管理员用户
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            is_staff=True  # 设置为管理员
        )
        
        # 创建用户资料
        UserProfile.objects.create(user=user)
        
        messages.success(request, '管理员注册成功，请登录')
        return redirect('management:login')
    
    return render(request, 'management/register.html')


@user_passes_test(is_admin)
def dashboard(request):
    """仪表盘"""
    # 统计数据
    total_users = User.objects.filter(is_staff=False).count()
    total_products = Product.objects.count()
    total_orders = Order.objects.count()
    total_sales = Order.objects.filter(status__in=['paid', 'shipped', 'delivered', 'completed']).aggregate(
        total=Sum('final_amount')
    )['total'] or 0
    
    # 订单状态统计
    order_stats = Order.objects.values('status').annotate(count=Count('id'))
    
    # 最新订单
    recent_orders = Order.objects.all()[:10]
    
    # 热门商品
    hot_products = Product.objects.filter(is_hot=True)[:5]
    
    context = {
        'total_users': total_users,
        'total_products': total_products,
        'total_orders': total_orders,
        'total_sales': total_sales,
        'order_stats': order_stats,
        'recent_orders': recent_orders,
        'hot_products': hot_products,
    }
    return render(request, 'management/dashboard.html', context)


@user_passes_test(is_admin)
def user_list(request):
    """用户列表"""
    users = User.objects.filter(is_staff=False)
    
    # 搜索
    search = request.GET.get('search')
    if search:
        users = users.filter(
            Q(username__icontains=search) |
            Q(email__icontains=search) |
            Q(first_name__icontains=search) |
            Q(last_name__icontains=search)
        )
    
    # 分页
    paginator = Paginator(users, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search': search,
    }
    return render(request, 'management/user_list.html', context)


@user_passes_test(is_admin)
def product_list(request):
    """商品列表"""
    products = Product.objects.all()
    
    # 搜索
    search = request.GET.get('search')
    if search:
        products = products.filter(name__icontains=search)
    
    # 分类筛选
    category_id = request.GET.get('category')
    if category_id:
        products = products.filter(category_id=category_id)
    
    # 状态筛选
    status = request.GET.get('status')
    if status == 'active':
        products = products.filter(is_active=True)
    elif status == 'inactive':
        products = products.filter(is_active=False)
    
    # 分页
    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # 分类列表
    categories = Category.objects.all()
    
    context = {
        'page_obj': page_obj,
        'categories': categories,
        'search': search,
        'current_category': category_id,
        'current_status': status,
    }
    return render(request, 'management/product_list.html', context)


@user_passes_test(is_admin)
def product_add(request):
    """添加商品"""
    if request.method == 'POST':
        name = request.POST.get('name')
        category_id = request.POST.get('category')
        description = request.POST.get('description')
        price = request.POST.get('price')
        original_price = request.POST.get('original_price')
        stock = request.POST.get('stock')
        main_image = request.FILES.get('main_image')
        is_hot = request.POST.get('is_hot') == 'on'
        is_new = request.POST.get('is_new') == 'on'
        is_active = request.POST.get('is_active') == 'on'
        
        # 验证
        if not all([name, category_id, description, price, stock, main_image]):
            messages.error(request, '请填写完整信息')
            return render(request, 'management/product_add.html', {
                'categories': Category.objects.all()
            })
        
        # 创建商品
        product = Product.objects.create(
            name=name,
            category_id=category_id,
            description=description,
            price=price,
            original_price=original_price or None,
            stock=stock,
            main_image=main_image,
            is_hot=is_hot,
            is_new=is_new,
            is_active=is_active,
        )
        
        messages.success(request, '商品添加成功')
        return redirect('management:product_list')
    
    categories = Category.objects.all()
    context = {
        'categories': categories,
    }
    return render(request, 'management/product_add.html', context)


@user_passes_test(is_admin)
def product_edit(request, product_id):
    """编辑商品"""
    product = get_object_or_404(Product, id=product_id)
    
    if request.method == 'POST':
        product.name = request.POST.get('name')
        product.category_id = request.POST.get('category')
        product.description = request.POST.get('description')
        product.price = request.POST.get('price')
        product.original_price = request.POST.get('original_price') or None
        product.stock = request.POST.get('stock')
        product.is_hot = request.POST.get('is_hot') == 'on'
        product.is_new = request.POST.get('is_new') == 'on'
        product.is_active = request.POST.get('is_active') == 'on'
        
        if 'main_image' in request.FILES:
            product.main_image = request.FILES['main_image']
        
        product.save()
        
        messages.success(request, '商品更新成功')
        return redirect('management:product_list')
    
    categories = Category.objects.all()
    context = {
        'product': product,
        'categories': categories,
    }
    return render(request, 'management/product_edit.html', context)


@user_passes_test(is_admin)
def product_delete(request, product_id):
    """删除商品"""
    product = get_object_or_404(Product, id=product_id)
    product.delete()
    messages.success(request, '商品删除成功')
    return redirect('management:product_list')


@user_passes_test(is_admin)
def category_list(request):
    """分类列表"""
    categories = Category.objects.all()
    
    context = {
        'categories': categories,
    }
    return render(request, 'management/category_list.html', context)


@user_passes_test(is_admin)
def order_list(request):
    """订单列表"""
    orders = Order.objects.all()
    
    # 搜索
    search = request.GET.get('search')
    if search:
        orders = orders.filter(
            Q(order_no__icontains=search) |
            Q(user__username__icontains=search) |
            Q(receiver_name__icontains=search)
        )
    
    # 状态筛选
    status = request.GET.get('status')
    if status:
        orders = orders.filter(status=status)
    
    # 分页
    paginator = Paginator(orders, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search': search,
        'current_status': status,
        'status_choices': Order.STATUS_CHOICES,
    }
    return render(request, 'management/order_list.html', context)


@user_passes_test(is_admin)
def order_detail(request, order_id):
    """订单详情"""
    order = get_object_or_404(Order, id=order_id)
    order_items = order.orderitem_set.all()
    
    context = {
        'order': order,
        'order_items': order_items,
    }
    return render(request, 'management/order_detail.html', context)


@user_passes_test(is_admin)
@csrf_exempt
def update_order_status(request):
    """更新订单状态"""
    if request.method == 'POST':
        data = json.loads(request.body)
        order_id = data.get('order_id')
        status = data.get('status')
        
        order = get_object_or_404(Order, id=order_id)
        order.status = status
        
        # 更新相关时间
        if status == 'paid':
            order.paid_at = timezone.now()
        elif status == 'shipped':
            order.shipped_at = timezone.now()
        elif status == 'delivered':
            order.delivered_at = timezone.now()
        
        order.save()
        
        return JsonResponse({'success': True, 'message': '订单状态更新成功'})
    
    return JsonResponse({'success': False, 'message': '请求错误'})
